<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automata Pharmacy</title>
    <link rel="stylesheet" href="node_modules/bootstrap/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="node_modules/@fortawesome/fontawesome-free/css/all.min.css">
    <link rel="stylesheet" href="node_modules/sweetalert2/dist/sweetalert2.min.css">
    <style>
        body { background-color: #f8f9fa; }
        .sidebar { min-height: 100vh; background-color: #343a40; }
        .sidebar .nav-link { color: #fff; }
        .sidebar .nav-link:hover { background-color: #495057; }
        .main-content { padding: 20px; }
        .card { box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }

        /* SweetAlert2 RTL fixes */
        .swal2-rtl {
            direction: rtl !important;
            text-align: right !important;
        }
        .swal2-rtl .swal2-title,
        .swal2-rtl .swal2-content,
        .swal2-rtl .swal2-html-container {
            text-align: right !important;
        }
        .swal2-rtl .swal2-actions {
            justify-content: flex-start !important;
        }

        /* Estilos para la tabla del carrito */
        #cartItems td {
            font-size: 0.85rem;
            padding: 0.4rem 0.5rem;
            vertical-align: middle;
        }
        #cartItems th {
            font-size: 0.9rem;
            padding: 0.5rem;
        }
        .input-group-sm > .form-control {
            padding: 0.25rem;
            font-size: 0.8rem;
            height: calc(1.5em + 0.5rem + 2px);
        }
        .input-group-sm > .btn {
            padding: 0.25rem 0.4rem;
            font-size: 0.8rem;
        }
        .btn-sm {
            padding: 0.25rem 0.4rem;
            font-size: 0.8rem;
        }

        /* Limitar texto largo */
        #cartItems td {
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* Estilos para el layout vertical */
        .pos-container {
            display: flex;
            flex-direction: column;
            height: calc(100vh - 40px);
        }

        .pos-main {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            margin-bottom: 15px;
        }

        .pos-cart {
            flex: 1;
            overflow: auto;
            min-height: 200px;
        }

        .pos-summary {
            height: auto;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar p-0">
                <div class="text-center py-4">
                    <i class="fas fa-clinic-medical fa-3x text-white"></i>
                    <h5 class="text-white mt-2">Automata Pharmacy</h5>
                </div>
                <nav class="nav flex-column mt-3">
                    <a class="nav-link active" href="index.html"><i class="fas fa-home me-2"></i>الرئيسية</a>
                    <a class="nav-link" href="index.html"><i class="fas fa-shopping-cart me-2"></i>نقطة البيع</a>
                    <a class="nav-link" href="inventory.html"><i class="fas fa-boxes me-2"></i>المخزون</a>
                    <a class="nav-link" href="prescriptions.html"><i class="fas fa-file-medical me-2"></i>الوصفات الطبية</a>
                    <a class="nav-link" href="reports.html"><i class="fas fa-chart-bar me-2"></i>التقارير</a>
                    <a class="nav-link" href="settings.html"><i class="fas fa-cog me-2"></i>الإعدادات</a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 main-content">
                <!-- POS Interface -->
                <div class="pos-container">
                    <!-- Main POS Area -->
                    <div class="pos-main">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">نقطة البيع</h5>
                            </div>
                            <div class="card-body d-flex flex-column">
                                <!-- Barcode Scanner -->
                                <div class="card mb-3 border-primary">
                                    <div class="card-body p-3">
                                        <h6 class="card-title mb-2"><i class="fas fa-barcode me-2"></i>ماسح الباركود</h6>
                                        <div class="input-group">
                                            <input type="text" class="form-control" placeholder="امسح الباركود أو أدخل الرمز يدوياً..." id="barcodeInput" autofocus>
                                            <button class="btn btn-primary" type="button" id="scanButton"><i class="fas fa-search"></i></button>
                                            <button class="btn btn-success" type="button" id="testBarcodeButton" title="اختبار الماسح الليزري"><i class="fas fa-barcode"></i></button>
                                        </div>
                                        <small class="text-muted">اضغط Enter بعد إدخال الباركود أو استخدم ماسح الباركود الليزري</small>
                                    </div>
                                </div>

                                <!-- Search Products -->
                                <div class="input-group mb-3">
                                    <input type="text" class="form-control" placeholder="بحث عن المنتج بالاسم..." id="searchProduct">
                                    <button class="btn btn-outline-secondary" type="button" id="searchButton"><i class="fas fa-search"></i></button>
                                    <button class="btn btn-outline-info" type="button" onclick="window.location.href='prescriptions.html'" title="الوصفات الطبية">
                                        <i class="fas fa-file-medical"></i>
                                    </button>
                                </div>

                                <div id="searchResults" class="mb-3" style="display: none;">
                                    <div class="list-group" id="productList">
                                        <!-- Search results will appear here -->
                                    </div>
                                </div>

                                <!-- Cart Table - Flex grow to fill available space -->
                                <div class="pos-cart">
                                    <div class="table-responsive h-100">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr style="height: 40px;">
                                                    <th style="width: 25%; font-size: 0.9rem; padding: 0.4rem 0.5rem; vertical-align: middle; height: 40px; line-height: 1;">المنتج</th>
                                                    <th style="width: 15%; font-size: 0.9rem; padding: 0.4rem 0.5rem; vertical-align: middle; height: 40px; line-height: 1;">الفئة</th>
                                                    <th style="width: 15%; font-size: 0.9rem; padding: 0.4rem 0.5rem; vertical-align: middle; height: 40px; line-height: 1;">السعر</th>
                                                    <th style="width: 20%; font-size: 0.9rem; padding: 0.4rem 0.5rem; vertical-align: middle; height: 40px; line-height: 1;">الكمية</th>
                                                    <th style="width: 15%; font-size: 0.9rem; padding: 0.4rem 0.5rem; vertical-align: middle; height: 40px; line-height: 1;">المجموع</th>
                                                    <th style="width: 10%; font-size: 0.9rem; padding: 0.4rem 0.5rem; vertical-align: middle; height: 40px; line-height: 1;"></th>
                                                </tr>
                                            </thead>
                                            <tbody id="cartItems"></tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Invoice Summary at the bottom -->
                    <div class="pos-summary">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">ملخص الفاتورة</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <!-- Left side: Totals -->
                                    <div class="col-md-6">
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>المجموع الفرعي:</span>
                                            <span id="subtotal">0.00</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>الضريبة:</span>
                                            <span id="tax">0.00</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-3">
                                            <span>الإجمالي:</span>
                                            <span id="total">0.00</span>
                                        </div>
                                    </div>

                                    <!-- Right side: Payment method and buttons -->
                                    <div class="col-md-6">
                                        <!-- Payment Method -->
                                        <div class="mb-3">
                                            <label class="form-label">طريقة الدفع:</label>
                                            <div class="d-flex">
                                                <div class="form-check me-3">
                                                    <input class="form-check-input" type="radio" name="paymentMethod" id="cashPayment" value="cash" checked>
                                                    <label class="form-check-label" for="cashPayment">
                                                        <i class="fas fa-money-bill-wave me-1"></i> نقدي
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="paymentMethod" id="cardPayment" value="card">
                                                    <label class="form-check-label" for="cardPayment">
                                                        <i class="fas fa-credit-card me-1"></i> بطاقة ائتمان
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Action Buttons -->
                                        <div class="d-flex flex-column gap-2">
                                            <div class="d-flex gap-2">
                                                <button class="btn btn-success flex-grow-1" id="completeSaleBtn">
                                                    <i class="fas fa-check me-2"></i>إتمام البيع
                                                </button>
                                                <button class="btn btn-primary flex-grow-1" id="completeSaleAndPrintBtn">
                                                    <i class="fas fa-print me-2"></i>إتمام البيع والطباعة
                                                </button>
                                            </div>
                                            <button class="btn btn-danger w-100" id="cancelSaleBtn">
                                                <i class="fas fa-times me-2"></i>إلغاء
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="pos.js"></script>
</body>
</html>