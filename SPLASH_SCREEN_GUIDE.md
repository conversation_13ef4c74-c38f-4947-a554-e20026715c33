# دليل شاشة البداية الديناميكية (Splash Screen)

## نظرة عامة

تم إضافة شاشة بداية ديناميكية جميلة لتطبيق Automata Pharmacy POS System. هذه الشاشة تظهر عند فتح التطبيق وتوفر تجربة مستخدم احترافية مع أنيميشن متطور.

## الميزات المضافة

### 1. شاشة البداية (Splash Screen)
- **المدة**: 4 ثوانٍ
- **الحجم**: 700x500 بكسل
- **التصميم**: شفاف بدون إطار
- **الموضع**: وسط الشاشة

### 2. التأثيرات البصرية

#### خلفية متدرجة متحركة
- تدرج لوني من الأزرق الداكن إلى الأزرق الفاتح
- تأثير تحريك الخلفية كل 8 ثوانٍ
- ألوان متناسقة مع هوية Automata Group

#### عناصر متحركة
- 6 أشكال هندسية متحركة في الخلفية
- حركة عائمة مع دوران
- شفافية متدرجة لتأثير عمق

#### شعار الشركة
- شعار SVG متحرك مع ترس دوار
- تأثير نبضة للخلفية
- نقاط متحركة حول الشعار
- تأثير توهج للنص

### 3. شريط التحميل التفاعلي

#### التصميم
- شريط تحميل متدرج اللون
- تأثير shimmer (لمعان متحرك)
- مدة التحميل: 4 ثوانٍ

#### النصوص التفاعلية
1. "جاري تحميل النظام..."
2. "جاري تحضير قاعدة البيانات..."
3. "جاري تهيئة واجهة المستخدم..."
4. "تم التحميل بنجاح!"

#### نقاط متحركة
- 3 نقاط متحركة بجانب النص
- تأثير bounce متتالي

### 4. معلومات الإصدار
- عرض رقم الإصدار (1.0.0)
- اسم المطور (Automata Group)
- أيقونة Font Awesome

## الملفات المضافة/المعدلة

### 1. ملفات جديدة
- `splash.html` - شاشة البداية الرئيسية

### 2. ملفات معدلة
- `main.js` - إضافة منطق إدارة شاشة البداية

## التفاصيل التقنية

### في main.js

```javascript
// متغيرات جديدة
let splashWindow;

// دالة إنشاء شاشة البداية
function createSplashWindow() {
  splashWindow = new BrowserWindow({
    width: 700,
    height: 500,
    frame: false,
    alwaysOnTop: true,
    transparent: true,
    center: true,
    resizable: false,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  splashWindow.loadFile('splash.html');
  
  // إغلاق splash screen بعد 4 ثوانٍ
  setTimeout(() => {
    createMainWindow();
    if (splashWindow) {
      splashWindow.close();
      splashWindow = null;
    }
  }, 4000);
}

// دالة إنشاء النافذة الرئيسية
function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    show: false, // لا تظهر فوراً
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  mainWindow.loadFile('index.html');

  // إظهار النافذة مع تأثير fade in
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    mainWindow.focus();
  });
}
```

### في splash.html

#### الأنيميشن الرئيسية
- `fadeInUp`: تأثير ظهور من الأسفل
- `logoAnimation`: تحريك الشعار
- `pulse`: تأثير النبضة
- `textGlow`: توهج النص
- `loading`: تحميل شريط التقدم
- `shimmer`: لمعان متحرك
- `float`: حركة عائمة للأشكال
- `backgroundShift`: تحريك الخلفية

#### الألوان المستخدمة
- الأزرق الداكن: `#1e3c72`
- الأزرق المتوسط: `#2a5298`
- الأزرق الفاتح: `#3b82f6`
- الأزرق الساطع: `#60a5fa`

## كيفية التخصيص

### تغيير مدة العرض
في `main.js`، غير القيمة في setTimeout:
```javascript
setTimeout(() => {
  // كود الإغلاق
}, 4000); // غير هذا الرقم (بالميلي ثانية)
```

### تغيير حجم النافذة
في `main.js`، غير قيم width و height:
```javascript
splashWindow = new BrowserWindow({
  width: 700,  // العرض
  height: 500, // الارتفاع
  // باقي الإعدادات
});
```

### تغيير النصوص
في `splash.html`، عدل مصفوفة loadingTexts:
```javascript
const loadingTexts = [
  'النص الأول...',
  'النص الثاني...',
  'النص الثالث...',
  'النص الأخير!'
];
```

### تغيير الألوان
في `splash.html`، عدل متغيرات CSS:
```css
background: linear-gradient(135deg, #لونك1, #لونك2, #لونك3);
```

## الاستخدام

1. شغل التطبيق بالأمر: `npm start`
2. ستظهر شاشة البداية لمدة 4 ثوانٍ
3. بعدها ستظهر النافذة الرئيسية تلقائياً

## المتطلبات

- Electron 25.3.1 أو أحدث
- اتصال بالإنترنت لتحميل Font Awesome (اختياري)

## الملاحظات

- شاشة البداية تظهر في المقدمة دائماً
- لا يمكن تغيير حجمها أو تحريكها
- تغلق تلقائياً بعد انتهاء المدة المحددة
- تدعم الشفافية والتأثيرات المتقدمة

## استكشاف الأخطاء

### إذا لم تظهر شاشة البداية
1. تأكد من وجود ملف `splash.html`
2. تحقق من console للأخطاء
3. تأكد من صحة مسار الملف

### إذا لم تظهر الأيقونات
1. تحقق من اتصال الإنترنت
2. أو استخدم ملف Font Awesome محلي

### إذا كانت الأنيميشن بطيئة
1. قلل عدد العناصر المتحركة
2. قلل مدة الأنيميشن
3. استخدم تأثيرات أبسط
