<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automata Pharmacy - Loading</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #3b82f6 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            animation: backgroundShift 8s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% {
                background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #3b82f6 100%);
            }
            50% {
                background: linear-gradient(135deg, #2a5298 0%, #3b82f6 50%, #1e3c72 100%);
            }
        }

        /* خلفية متحركة */
        .background-animation {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            top: 10%;
            left: 10%;
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            top: 20%;
            right: 15%;
            width: 60px;
            height: 60px;
            background: rgba(59, 130, 246, 0.2);
            border-radius: 30%;
            animation-delay: 1s;
        }

        .shape:nth-child(3) {
            bottom: 20%;
            left: 20%;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20%;
            animation-delay: 2s;
        }

        .shape:nth-child(4) {
            bottom: 30%;
            right: 10%;
            width: 70px;
            height: 70px;
            background: rgba(59, 130, 246, 0.15);
            border-radius: 50%;
            animation-delay: 3s;
        }

        .shape:nth-child(5) {
            top: 50%;
            left: 5%;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 50%;
            animation-delay: 4s;
        }

        .shape:nth-child(6) {
            top: 70%;
            right: 25%;
            width: 90px;
            height: 90px;
            background: rgba(59, 130, 246, 0.1);
            border-radius: 20%;
            animation-delay: 5s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        /* الحاوية الرئيسية */
        .splash-container {
            text-align: center;
            z-index: 10;
            position: relative;
            animation: fadeInUp 1s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* شعار الشركة */
        .logo-container {
            margin-bottom: 30px;
            position: relative;
        }

        .logo {
            width: 200px;
            height: 200px;
            margin: 0 auto;
            position: relative;
            animation: logoAnimation 2s ease-in-out infinite;
        }

        @keyframes logoAnimation {
            0%, 100% {
                transform: scale(1) rotate(0deg);
            }
            50% {
                transform: scale(1.05) rotate(5deg);
            }
        }

        .logo-bg {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 220px;
            height: 220px;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 50%, transparent 70%);
            border-radius: 50%;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 0.5;
            }
            50% {
                transform: translate(-50%, -50%) scale(1.1);
                opacity: 0.8;
            }
        }

        /* النص */
        .company-name {
            color: white;
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: textGlow 2s ease-in-out infinite alternate;
        }

        @keyframes textGlow {
            from {
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 0 0 10px rgba(255,255,255,0.2);
            }
            to {
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 0 0 20px rgba(255,255,255,0.4);
            }
        }

        .tagline {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.2rem;
            margin-bottom: 40px;
            font-weight: 300;
        }

        /* شريط التحميل */
        .loading-container {
            width: 300px;
            margin: 0 auto;
        }

        .loading-text {
            color: white;
            font-size: 1rem;
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #60a5fa, #3b82f6, #1d4ed8);
            border-radius: 2px;
            animation: loading 4s ease-in-out;
            transform-origin: left;
            position: relative;
            overflow: hidden;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes loading {
            0% {
                transform: scaleX(0);
            }
            100% {
                transform: scaleX(1);
            }
        }

        @keyframes shimmer {
            0% {
                left: -100%;
            }
            100% {
                left: 100%;
            }
        }

        /* نقاط متحركة */
        .loading-dots {
            display: inline-block;
            margin-left: 5px;
        }

        .dot {
            display: inline-block;
            width: 6px;
            height: 6px;
            background: white;
            border-radius: 50%;
            margin: 0 2px;
            animation: dotBounce 1.4s ease-in-out infinite both;
        }

        .dot:nth-child(1) { animation-delay: -0.32s; }
        .dot:nth-child(2) { animation-delay: -0.16s; }
        .dot:nth-child(3) { animation-delay: 0s; }

        @keyframes dotBounce {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* تأثير الخروج */
        .fade-out {
            animation: fadeOut 0.8s ease-in-out forwards;
        }

        @keyframes fadeOut {
            0% {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
            50% {
                opacity: 0.5;
                transform: scale(1.05) translateY(-10px);
            }
            100% {
                opacity: 0;
                transform: scale(0.95) translateY(20px);
            }
        }

        /* تأثير إضافي للنص */
        .version-info {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            animation: fadeInUp 1s ease-out 1s both;
        }
    </style>
</head>
<body>
    <!-- خلفية متحركة -->
    <div class="background-animation">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="splash-container" id="splashContainer">
        <!-- الشعار -->
        <div class="logo-container">
            <div class="logo-bg"></div>
            <div class="logo">
                <!-- SVG Logo -->
                <svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                    <!-- خلفية دائرية -->
                    <circle cx="100" cy="100" r="90" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
                    
                    <!-- الترس الرئيسي -->
                    <g transform="translate(100,100)">
                        <circle r="35" fill="rgba(255,255,255,0.9)" stroke="#1e3c72" stroke-width="3"/>
                        <circle r="15" fill="#3b82f6"/>
                        
                        <!-- أسنان الترس -->
                        <g stroke="#1e3c72" stroke-width="2" fill="rgba(255,255,255,0.8)">
                            <rect x="-3" y="-45" width="6" height="10" rx="2"/>
                            <rect x="-3" y="35" width="6" height="10" rx="2"/>
                            <rect x="35" y="-3" width="10" height="6" rx="2"/>
                            <rect x="-45" y="-3" width="10" height="6" rx="2"/>
                            
                            <rect x="25" y="-32" width="8" height="6" rx="2" transform="rotate(45)"/>
                            <rect x="-32" y="-32" width="8" height="6" rx="2" transform="rotate(-45)"/>
                            <rect x="25" y="25" width="8" height="6" rx="2" transform="rotate(-45)"/>
                            <rect x="-32" y="25" width="8" height="6" rx="2" transform="rotate(45)"/>
                        </g>
                    </g>
                    
                    <!-- خطوط الدوائر -->
                    <g stroke="rgba(255,255,255,0.6)" stroke-width="2" fill="none">
                        <path d="M 30 100 Q 50 80 70 100 Q 50 120 30 100"/>
                        <path d="M 170 100 Q 150 80 130 100 Q 150 120 170 100"/>
                        <path d="M 100 30 Q 120 50 100 70 Q 80 50 100 30"/>
                        <path d="M 100 170 Q 120 150 100 130 Q 80 150 100 170"/>
                    </g>
                    
                    <!-- نقاط متحركة -->
                    <circle cx="50" cy="100" r="4" fill="#60a5fa">
                        <animate attributeName="r" values="4;6;4" dur="2s" repeatCount="indefinite"/>
                    </circle>
                    <circle cx="150" cy="100" r="4" fill="#60a5fa">
                        <animate attributeName="r" values="4;6;4" dur="2s" begin="0.5s" repeatCount="indefinite"/>
                    </circle>
                    <circle cx="100" cy="50" r="4" fill="#60a5fa">
                        <animate attributeName="r" values="4;6;4" dur="2s" begin="1s" repeatCount="indefinite"/>
                    </circle>
                    <circle cx="100" cy="150" r="4" fill="#60a5fa">
                        <animate attributeName="r" values="4;6;4" dur="2s" begin="1.5s" repeatCount="indefinite"/>
                    </circle>
                    
                    <!-- تأثير دوران -->
                    <animateTransform attributeName="transform" type="rotate" values="0 100 100;***********" dur="8s" repeatCount="indefinite"/>
                </svg>
            </div>
        </div>

        <!-- اسم الشركة -->
        <h1 class="company-name">AUTOMATA GROUP</h1>
        <p class="tagline">نظام إدارة الصيدلية المتطور</p>

        <!-- شريط التحميل -->
        <div class="loading-container">
            <div class="loading-text">
                جاري التحميل
                <span class="loading-dots">
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                </span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
        </div>

        <!-- معلومات الإصدار -->
        <div class="version-info">
            <i class="fas fa-code"></i> الإصدار 1.0.0 | تطوير Automata Group
        </div>
    </div>

    <script>
        // تأثيرات إضافية عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثير صوتي (اختياري)
            console.log('🚀 Automata Pharmacy System Loading...');

            // تحديث نص التحميل
            const loadingTexts = [
                'جاري تحميل النظام...',
                'جاري تحضير قاعدة البيانات...',
                'جاري تهيئة واجهة المستخدم...',
                'تم التحميل بنجاح!'
            ];

            const loadingTextElement = document.querySelector('.loading-text');
            let currentTextIndex = 0;

            const textInterval = setInterval(() => {
                if (currentTextIndex < loadingTexts.length - 1) {
                    currentTextIndex++;
                    loadingTextElement.innerHTML = loadingTexts[currentTextIndex] +
                        '<span class="loading-dots"><span class="dot"></span><span class="dot"></span><span class="dot"></span></span>';
                } else {
                    clearInterval(textInterval);
                }
            }, 1000);
        });

        // تأثير الخروج قبل إغلاق النافذة
        setTimeout(() => {
            document.getElementById('splashContainer').classList.add('fade-out');
        }, 3500);
    </script>
</body>
</html>
