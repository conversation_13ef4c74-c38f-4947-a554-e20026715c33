const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// متغيرات عامة
let salesData = [];
let productsData = [];
let prescriptionsData = [];
let salesChart = null;
let categoryChart = null;

// عناصر DOM
const startDateInput = document.getElementById('startDate');
const endDateInput = document.getElementById('endDate');
const updateReportsBtn = document.getElementById('updateReports');
const autoUpdateToggle = document.getElementById('autoUpdateToggle');
const resetDataBtn = document.getElementById('resetDataBtn');

// عناصر الإحصائيات
const totalSalesElement = document.getElementById('totalSales');
const transactionCountElement = document.getElementById('transactionCount');
const prescriptionCountElement = document.getElementById('prescriptionCount');
const lowStockCountElement = document.getElementById('lowStockCount');
const recentSalesTable = document.getElementById('recentSalesTable');
const noSalesMessage = document.getElementById('noSalesMessage');

// عناصر حالة الاتصال
const connectionStatusElement = document.getElementById('connectionStatus');
const lastUpdateTimeElement = document.getElementById('lastUpdateTimeValue');

// تحميل البيانات عند بدء الصفحة
document.addEventListener('DOMContentLoaded', async () => {
    console.log('تحميل صفحة التقارير...');
    await initializeReports();
    setupEventListeners();
    setDefaultDateRange();
});

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // تحديث التقارير
    if (updateReportsBtn) {
        updateReportsBtn.addEventListener('click', updateReports);
    }
    
    // تغيير التواريخ
    if (startDateInput) {
        startDateInput.addEventListener('change', () => {
            if (autoUpdateToggle.checked) {
                updateReports();
            }
        });
    }
    
    if (endDateInput) {
        endDateInput.addEventListener('change', () => {
            if (autoUpdateToggle.checked) {
                updateReports();
            }
        });
    }
    
    // مسح البيانات
    if (resetDataBtn) {
        resetDataBtn.addEventListener('click', resetAllData);
    }
}

// تعيين نطاق التواريخ الافتراضي
function setDefaultDateRange() {
    const today = new Date();
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
    
    if (startDateInput) {
        startDateInput.value = lastMonth.toISOString().split('T')[0];
    }
    
    if (endDateInput) {
        endDateInput.value = today.toISOString().split('T')[0];
    }
}

// تهيئة التقارير
async function initializeReports() {
    try {
        await loadAllData();
        updateStatistics();
        createCharts();
        updateRecentSalesTable();
        console.log('تم تحميل التقارير بنجاح');
    } catch (error) {
        console.error('خطأ في تهيئة التقارير:', error);
        showAlert('فشل في تحميل التقارير', 'error');
    }
}

// تحميل جميع البيانات
async function loadAllData() {
    try {
        // تحميل المبيعات
        salesData = await ipcRenderer.invoke('get-sales') || [];
        
        // تحميل المنتجات
        productsData = await ipcRenderer.invoke('get-products') || [];
        
        // تحميل الوصفات
        prescriptionsData = await ipcRenderer.invoke('get-prescriptions') || [];
        
        console.log(`تم تحميل ${salesData.length} مبيعة، ${productsData.length} منتج، ${prescriptionsData.length} وصفة`);
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        throw error;
    }
}

// تحديث التقارير
async function updateReports() {
    try {
        showLoading(true);
        updateConnectionStatus('updating');

        await loadAllData();
        updateStatisticsEnhanced();
        updateCharts();
        updateRecentSalesTable();
        updateLastUpdateTime();

        updateConnectionStatus('connected');
        showAlert('تم تحديث التقارير بنجاح', 'success');
    } catch (error) {
        console.error('خطأ في تحديث التقارير:', error);
        updateConnectionStatus('error');
        showAlert('فشل في تحديث التقارير', 'error');
    } finally {
        showLoading(false);
    }
}

// تحديث الإحصائيات
function updateStatistics() {
    const dateRange = getDateRange();
    const filteredSales = filterSalesByDate(salesData, dateRange);
    
    // إجمالي المبيعات
    const totalSales = filteredSales.reduce((sum, sale) => sum + sale.total, 0);
    if (totalSalesElement) {
        totalSalesElement.textContent = totalSales.toFixed(2);
    }
    
    // عدد المعاملات
    if (transactionCountElement) {
        transactionCountElement.textContent = filteredSales.length;
    }
    
    // عدد الوصفات
    const filteredPrescriptions = filterPrescriptionsByDate(prescriptionsData, dateRange);
    if (prescriptionCountElement) {
        prescriptionCountElement.textContent = filteredPrescriptions.length;
    }
    
    // المنتجات منخفضة المخزون (أقل من 10)
    const lowStockProducts = productsData.filter(product => product.quantity < 10);
    if (lowStockCountElement) {
        lowStockCountElement.textContent = lowStockProducts.length;
    }
}

// إنشاء الرسوم البيانية
function createCharts() {
    createSalesChart();
    createCategoryChart();
}

// إنشاء رسم المبيعات اليومية
function createSalesChart() {
    const ctx = document.getElementById('salesChart');
    if (!ctx) return;
    
    // تدمير الرسم السابق إن وجد
    if (salesChart) {
        salesChart.destroy();
    }
    
    const dateRange = getDateRange();
    const filteredSales = filterSalesByDate(salesData, dateRange);
    
    // تجميع المبيعات حسب التاريخ
    const salesByDate = {};
    filteredSales.forEach(sale => {
        const date = new Date(sale.timestamp).toLocaleDateString('ar-SA');
        salesByDate[date] = (salesByDate[date] || 0) + sale.total;
    });
    
    const labels = Object.keys(salesByDate).sort();
    const data = labels.map(date => salesByDate[date]);
    
    salesChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'المبيعات اليومية',
                data: data,
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toFixed(2) + ' ر.س';
                        }
                    }
                }
            }
        }
    });
}

// إنشاء رسم توزيع المبيعات حسب الفئة
function createCategoryChart() {
    const ctx = document.getElementById('categoryChart');
    if (!ctx) return;
    
    // تدمير الرسم السابق إن وجد
    if (categoryChart) {
        categoryChart.destroy();
    }
    
    const dateRange = getDateRange();
    const filteredSales = filterSalesByDate(salesData, dateRange);
    
    // تجميع المبيعات حسب الفئة
    const salesByCategory = {};
    filteredSales.forEach(sale => {
        sale.items.forEach(item => {
            const product = productsData.find(p => p.id === item.id);
            if (product) {
                const category = getCategoryName(product.category);
                salesByCategory[category] = (salesByCategory[category] || 0) + (item.price * item.quantity);
            }
        });
    });
    
    const labels = Object.keys(salesByCategory);
    const data = Object.values(salesByCategory);
    const colors = [
        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
        '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
    ];
    
    categoryChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors.slice(0, labels.length),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// تحديث جدول المبيعات الأخيرة
function updateRecentSalesTable() {
    if (!recentSalesTable) return;
    
    const dateRange = getDateRange();
    const filteredSales = filterSalesByDate(salesData, dateRange);
    const recentSales = filteredSales.slice(-10).reverse(); // آخر 10 مبيعات
    
    if (recentSales.length === 0) {
        recentSalesTable.innerHTML = '';
        if (noSalesMessage) {
            noSalesMessage.style.display = 'block';
        }
        return;
    }
    
    if (noSalesMessage) {
        noSalesMessage.style.display = 'none';
    }
    
    recentSalesTable.innerHTML = recentSales.map(sale => `
        <tr>
            <td><strong>#${sale.id}</strong></td>
            <td>${formatDateTime(sale.timestamp)}</td>
            <td>
                <small>
                    ${sale.items.map(item => {
                        const product = productsData.find(p => p.id === item.id);
                        return `${product ? product.name : 'منتج محذوف'} (${item.quantity})`;
                    }).join(', ')}
                </small>
            </td>
            <td><strong>${sale.total.toFixed(2)} ر.س</strong></td>
        </tr>
    `).join('');
}

// تحديث الرسوم البيانية
function updateCharts() {
    createSalesChart();
    createCategoryChart();
}

// الحصول على نطاق التواريخ
function getDateRange() {
    const startDate = startDateInput ? new Date(startDateInput.value) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const endDate = endDateInput ? new Date(endDateInput.value) : new Date();

    // تعيين الوقت لبداية ونهاية اليوم
    startDate.setHours(0, 0, 0, 0);
    endDate.setHours(23, 59, 59, 999);

    return { startDate, endDate };
}

// تصفية المبيعات حسب التاريخ
function filterSalesByDate(sales, dateRange) {
    return sales.filter(sale => {
        const saleDate = new Date(sale.timestamp);
        return saleDate >= dateRange.startDate && saleDate <= dateRange.endDate;
    });
}

// تصفية الوصفات حسب التاريخ
function filterPrescriptionsByDate(prescriptions, dateRange) {
    return prescriptions.filter(prescription => {
        const prescriptionDate = new Date(prescription.date);
        return prescriptionDate >= dateRange.startDate && prescriptionDate <= dateRange.endDate;
    });
}

// الحصول على اسم الفئة
function getCategoryName(category) {
    const categories = {
        'medicine': 'أدوية',
        'supplements': 'مكملات غذائية',
        'cosmetics': 'مستحضرات تجميل',
        'medical_devices': 'أجهزة طبية',
        'baby_care': 'منتجات الأطفال',
        'personal_care': 'العناية الشخصية',
        'other': 'أخرى'
    };
    return categories[category] || 'غير محدد';
}

// تنسيق التاريخ والوقت
function formatDateTime(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleString('ar-SA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// مسح جميع البيانات
async function resetAllData() {
    try {
        const result = await Swal.fire({
            title: 'تأكيد مسح البيانات',
            text: 'هل أنت متأكد من رغبتك في مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'نعم، امسح البيانات',
            cancelButtonText: 'إلغاء',
            reverseButtons: true
        });

        if (result.isConfirmed) {
            showLoading(true);

            // مسح المبيعات
            await ipcRenderer.invoke('clear-sales');

            // مسح الوصفات
            await ipcRenderer.invoke('clear-prescriptions');

            // إعادة تعيين كميات المنتجات (اختياري)
            const resetInventory = await Swal.fire({
                title: 'إعادة تعيين المخزون؟',
                text: 'هل تريد أيضاً إعادة تعيين كميات المنتجات في المخزون؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم',
                cancelButtonText: 'لا'
            });

            if (resetInventory.isConfirmed) {
                await ipcRenderer.invoke('reset-inventory');
            }

            // إعادة تحميل البيانات
            await loadAllData();
            updateStatistics();
            updateCharts();
            updateRecentSalesTable();

            showAlert('تم مسح جميع البيانات بنجاح', 'success');
        }
    } catch (error) {
        console.error('خطأ في مسح البيانات:', error);
        showAlert('فشل في مسح البيانات', 'error');
    } finally {
        showLoading(false);
    }
}

// عرض حالة التحميل
function showLoading(show) {
    if (updateReportsBtn) {
        if (show) {
            updateReportsBtn.disabled = true;
            updateReportsBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحديث...';
        } else {
            updateReportsBtn.disabled = false;
            updateReportsBtn.innerHTML = '<i class="fas fa-sync-alt me-2"></i>تحديث التقارير';
        }
    }
}

// عرض رسائل التنبيه
function showAlert(message, type = 'info') {
    const alertTypes = {
        'success': { icon: 'success', color: '#28a745' },
        'error': { icon: 'error', color: '#dc3545' },
        'warning': { icon: 'warning', color: '#ffc107' },
        'info': { icon: 'info', color: '#17a2b8' }
    };

    const alertConfig = alertTypes[type] || alertTypes['info'];

    Swal.fire({
        icon: alertConfig.icon,
        title: message,
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        toast: true,
        position: 'top-end'
    });
}

// تصدير التقارير إلى PDF
async function exportToPDF() {
    try {
        showLoading(true);

        const dateRange = getDateRange();
        const reportData = {
            dateRange: {
                start: dateRange.startDate.toLocaleDateString('ar-SA'),
                end: dateRange.endDate.toLocaleDateString('ar-SA')
            },
            statistics: {
                totalSales: totalSalesElement.textContent,
                transactionCount: transactionCountElement.textContent,
                prescriptionCount: prescriptionCountElement.textContent,
                lowStockCount: lowStockCountElement.textContent
            },
            sales: filterSalesByDate(salesData, dateRange),
            products: productsData,
            prescriptions: filterPrescriptionsByDate(prescriptionsData, dateRange)
        };

        await ipcRenderer.invoke('export-report-pdf', reportData);
        showAlert('تم تصدير التقرير بنجاح', 'success');

    } catch (error) {
        console.error('خطأ في تصدير التقرير:', error);
        showAlert('فشل في تصدير التقرير', 'error');
    } finally {
        showLoading(false);
    }
}

// تصدير التقارير إلى Excel
async function exportToExcel() {
    try {
        showLoading(true);

        const dateRange = getDateRange();
        const reportData = {
            dateRange: {
                start: dateRange.startDate.toLocaleDateString('ar-SA'),
                end: dateRange.endDate.toLocaleDateString('ar-SA')
            },
            sales: filterSalesByDate(salesData, dateRange),
            products: productsData,
            prescriptions: filterPrescriptionsByDate(prescriptionsData, dateRange)
        };

        await ipcRenderer.invoke('export-report-excel', reportData);
        showAlert('تم تصدير التقرير بنجاح', 'success');

    } catch (error) {
        console.error('خطأ في تصدير التقرير:', error);
        showAlert('فشل في تصدير التقرير', 'error');
    } finally {
        showLoading(false);
    }
}

// طباعة التقرير
function printReport() {
    window.print();
}

// مستمع لتحديث البيانات تلقائياً
ipcRenderer.on('data-updated', async () => {
    if (autoUpdateToggle && autoUpdateToggle.checked) {
        console.log('تحديث تلقائي للتقارير...');
        await updateReports();
    }
});

// مستمع لإشعارات إتمام البيع
ipcRenderer.on('sale-completed', async (event, saleData) => {
    console.log('🎉 تم إتمام عملية بيع جديدة:', saleData);

    // تحديث فوري للتقارير إذا كان التحديث التلقائي مفعل
    if (autoUpdateToggle && autoUpdateToggle.checked) {
        console.log('تحديث فوري للتقارير بعد البيع...');

        // إضافة تأثير بصري للإشعار
        showSaleNotification(saleData);

        // تحديث البيانات مع تأخير قصير للتأثير البصري
        setTimeout(async () => {
            await loadAllData();
            updateStatisticsEnhanced();
            updateCharts();
            updateRecentSalesTable();
            updateLastUpdateTime();
            updateConnectionStatus('connected');
        }, 1000);
    } else {
        // عرض إشعار بوجود بيانات جديدة
        showUpdateAvailableNotification();
    }
});

// عرض إشعار البيع الجديد
function showSaleNotification(saleData) {
    const toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 4000,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.addEventListener('mouseenter', Swal.stopTimer);
            toast.addEventListener('mouseleave', Swal.resumeTimer);
        }
    });

    toast.fire({
        icon: 'success',
        title: 'عملية بيع جديدة!',
        html: `
            <div class="text-start">
                <strong>رقم العملية:</strong> #${saleData.saleId}<br>
                <strong>المبلغ:</strong> ${saleData.saleRecord.total.toFixed(2)} ر.س<br>
                <strong>المنتجات:</strong> ${saleData.saleRecord.items.length} منتج
            </div>
        `,
        background: '#d4edda',
        color: '#155724'
    });
}

// عرض إشعار توفر تحديث
function showUpdateAvailableNotification() {
    const toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: true,
        confirmButtonText: 'تحديث الآن',
        showCancelButton: true,
        cancelButtonText: 'لاحقاً',
        timer: 8000,
        timerProgressBar: true
    });

    toast.fire({
        icon: 'info',
        title: 'بيانات جديدة متوفرة',
        text: 'تم إتمام عملية بيع جديدة. هل تريد تحديث التقارير؟',
        background: '#d1ecf1',
        color: '#0c5460'
    }).then((result) => {
        if (result.isConfirmed) {
            updateReports();
        }
    });
}

// تحديث الإحصائيات مع تأثيرات بصرية
async function updateStatisticsWithAnimation() {
    // إضافة تأثير تحميل للبطاقات
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.classList.add('loading');
    });

    // تحديث البيانات
    await loadAllData();
    updateStatistics();

    // إزالة تأثير التحميل مع تأخير
    setTimeout(() => {
        statCards.forEach(card => {
            card.classList.remove('loading');
            // إضافة تأثير نبضة للإشارة للتحديث
            card.style.animation = 'pulse 0.6s ease-in-out';
            setTimeout(() => {
                card.style.animation = '';
            }, 600);
        });
    }, 500);
}

// إضافة تأثير النبضة للـ CSS
const pulseStyle = document.createElement('style');
pulseStyle.textContent = `
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .stat-card.loading {
        opacity: 0.7;
        position: relative;
    }

    .stat-card.loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 20px;
        height: 20px;
        margin: -10px 0 0 -10px;
        border: 2px solid rgba(255,255,255,0.3);
        border-top: 2px solid rgba(255,255,255,0.8);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
`;
document.head.appendChild(pulseStyle);

// تحديث حالة الاتصال
function updateConnectionStatus(status) {
    if (!connectionStatusElement) return;

    const statusConfig = {
        'connected': {
            class: 'bg-success',
            icon: 'fas fa-wifi',
            text: 'متصل مع نقطة البيع'
        },
        'updating': {
            class: 'bg-warning',
            icon: 'fas fa-sync-alt fa-spin',
            text: 'جاري التحديث...'
        },
        'error': {
            class: 'bg-danger',
            icon: 'fas fa-exclamation-triangle',
            text: 'خطأ في الاتصال'
        },
        'disconnected': {
            class: 'bg-secondary',
            icon: 'fas fa-wifi-slash',
            text: 'غير متصل'
        }
    };

    const config = statusConfig[status] || statusConfig['disconnected'];

    connectionStatusElement.className = `badge ${config.class} me-3`;
    connectionStatusElement.innerHTML = `
        <i class="${config.icon} me-1"></i>
        ${config.text}
    `;
}

// تحديث وقت آخر تحديث
function updateLastUpdateTime() {
    if (!lastUpdateTimeElement) return;

    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    lastUpdateTimeElement.textContent = timeString;

    // إضافة تأثير بصري للتحديث
    lastUpdateTimeElement.style.color = '#28a745';
    lastUpdateTimeElement.style.fontWeight = 'bold';

    setTimeout(() => {
        lastUpdateTimeElement.style.color = '';
        lastUpdateTimeElement.style.fontWeight = '';
    }, 2000);
}

// مراقبة حالة الاتصال
function monitorConnection() {
    // تحديث حالة الاتصال كل 30 ثانية
    setInterval(async () => {
        try {
            // اختبار الاتصال بالعملية الرئيسية
            await ipcRenderer.invoke('get-sales', { limit: 1 });
            updateConnectionStatus('connected');
        } catch (error) {
            console.error('فقدان الاتصال:', error);
            updateConnectionStatus('error');
        }
    }, 30000);
}

// بدء مراقبة الاتصال عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    updateConnectionStatus('connected');
    updateLastUpdateTime();
    monitorConnection();
});

// تحديث محسن للإحصائيات مع تأثيرات بصرية
async function updateStatisticsEnhanced() {
    const statCards = document.querySelectorAll('.stat-card');

    // إضافة تأثير تحميل
    statCards.forEach(card => {
        card.style.transform = 'scale(0.95)';
        card.style.opacity = '0.7';
        card.style.transition = 'all 0.3s ease';
    });

    // تحديث البيانات
    const dateRange = getDateRange();
    const filteredSales = filterSalesByDate(salesData, dateRange);

    // تحديث كل إحصائية مع تأخير تدريجي
    setTimeout(() => {
        const totalSales = filteredSales.reduce((sum, sale) => sum + sale.total, 0);
        if (totalSalesElement) {
            animateNumber(totalSalesElement, totalSales, 2);
        }
    }, 100);

    setTimeout(() => {
        if (transactionCountElement) {
            animateNumber(transactionCountElement, filteredSales.length, 0);
        }
    }, 200);

    setTimeout(() => {
        const filteredPrescriptions = filterPrescriptionsByDate(prescriptionsData, dateRange);
        if (prescriptionCountElement) {
            animateNumber(prescriptionCountElement, filteredPrescriptions.length, 0);
        }
    }, 300);

    setTimeout(() => {
        const lowStockProducts = productsData.filter(product => product.quantity < 10);
        if (lowStockCountElement) {
            animateNumber(lowStockCountElement, lowStockProducts.length, 0);
        }
    }, 400);

    // إزالة تأثير التحميل
    setTimeout(() => {
        statCards.forEach(card => {
            card.style.transform = 'scale(1)';
            card.style.opacity = '1';
        });
    }, 500);
}

// تحريك الأرقام
function animateNumber(element, targetValue, decimals = 0) {
    const startValue = parseFloat(element.textContent) || 0;
    const increment = (targetValue - startValue) / 20;
    let currentValue = startValue;

    const timer = setInterval(() => {
        currentValue += increment;

        if ((increment > 0 && currentValue >= targetValue) ||
            (increment < 0 && currentValue <= targetValue)) {
            currentValue = targetValue;
            clearInterval(timer);
        }

        element.textContent = currentValue.toFixed(decimals);
    }, 50);
}

// تصدير الدوال للاستخدام العام
window.reportsModule = {
    updateReports,
    resetAllData,
    exportToPDF,
    exportToExcel,
    printReport
};
