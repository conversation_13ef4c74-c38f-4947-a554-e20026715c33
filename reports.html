<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automata Pharmacy - التقارير</title>
    <link rel="stylesheet" href="node_modules/bootstrap/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="node_modules/@fortawesome/fontawesome-free/css/all.min.css">
    <style>
        body { background-color: #f8f9fa; }
        .sidebar { min-height: 100vh; background-color: #343a40; }
        .sidebar .nav-link { color: #fff; }
        .sidebar .nav-link:hover { background-color: #495057; }
        .main-content { padding: 20px; }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: none;
            border-radius: 10px;
        }
        .stat-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        /* تحسين الأزرار */
        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        /* تحسين الجداول */
        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        /* تحسين الرسوم البيانية */
        .chart-container {
            position: relative;
            height: 300px;
        }

        /* تحسين النماذج */
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        /* تحسين البطاقات الإحصائية */
        .stat-card .card-body {
            padding: 1.5rem;
        }

        .stat-card h3 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-card h6 {
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* تأثيرات التحميل */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* تحسين الرسائل */
        .no-data-message {
            padding: 3rem;
            text-align: center;
            color: #6c757d;
        }

        .no-data-message i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* تحسين مجموعة الأزرار */
        .btn-group .btn {
            border-radius: 0;
        }

        .btn-group .btn:first-child {
            border-top-right-radius: 8px;
            border-bottom-right-radius: 8px;
        }

        .btn-group .btn:last-child {
            border-top-left-radius: 8px;
            border-bottom-left-radius: 8px;
        }

        /* تحسين التبديل */
        .form-check-input:checked {
            background-color: #007bff;
            border-color: #007bff;
        }

        /* مؤشر الاتصال */
        #connectionStatus {
            font-size: 0.85rem;
            padding: 0.5rem 0.75rem;
            border-radius: 20px;
            transition: all 0.3s ease;
            animation: fadeIn 0.5s ease-in-out;
        }

        #connectionStatus.bg-success {
            background: linear-gradient(135deg, #28a745, #20c997) !important;
        }

        #connectionStatus.bg-warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
        }

        #connectionStatus.bg-danger {
            background: linear-gradient(135deg, #dc3545, #e83e8c) !important;
        }

        #lastUpdateTime {
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* تحسين الأرقام المتحركة */
        .stat-card h3 {
            transition: all 0.3s ease;
            font-variant-numeric: tabular-nums;
        }

        .stat-card.updating h3 {
            color: rgba(255, 255, 255, 0.7);
        }

        /* طباعة */
        @media print {
            .sidebar, .btn, .form-control, .form-select, #connectionStatus, #lastUpdateTime {
                display: none !important;
            }

            .main-content {
                margin: 0 !important;
                padding: 0 !important;
            }

            .card {
                box-shadow: none !important;
                border: 1px solid #dee2e6 !important;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar p-0">
                <div class="text-center py-4">
                    <i class="fas fa-clinic-medical fa-3x text-white"></i>
                    <h5 class="text-white mt-2">Automata Pharmacy</h5>
                </div>
                <nav class="nav flex-column mt-3">
                    <a class="nav-link" href="index.html"><i class="fas fa-home me-2"></i>الرئيسية</a>
                    <a class="nav-link" href="index.html"><i class="fas fa-shopping-cart me-2"></i>نقطة البيع</a>
                    <a class="nav-link" href="inventory.html"><i class="fas fa-boxes me-2"></i>المخزون</a>
                    <a class="nav-link" href="prescriptions.html"><i class="fas fa-file-medical me-2"></i>الوصفات الطبية</a>
                    <a class="nav-link active" href="reports.html"><i class="fas fa-chart-bar me-2"></i>التقارير</a>
                    <a class="nav-link" href="settings.html"><i class="fas fa-cog me-2"></i>الإعدادات</a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="mb-0">لوحة التحكم والتقارير</h2>
                    <div class="d-flex align-items-center">
                        <div id="connectionStatus" class="badge bg-success me-3">
                            <i class="fas fa-wifi me-1"></i>
                            متصل مع نقطة البيع
                        </div>
                        <div id="lastUpdateTime" class="text-muted small">
                            آخر تحديث: <span id="lastUpdateTimeValue">--</span>
                        </div>
                    </div>
                </div>

                <!-- Date Range Filter -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-end">
                            <div class="col-md-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="startDate">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="endDate">
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-primary w-100" id="updateReports">
                                    <i class="fas fa-sync-alt me-2"></i>تحديث التقارير
                                </button>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="autoUpdateToggle" checked>
                                    <label class="form-check-label" for="autoUpdateToggle">تحديث تلقائي عند إضافة مبيعات</label>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="btn-group" role="group">
                                    <button class="btn btn-success" onclick="reportsModule.exportToExcel()">
                                        <i class="fas fa-file-excel me-2"></i>تصدير Excel
                                    </button>
                                    <button class="btn btn-info" onclick="reportsModule.exportToPDF()">
                                        <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                                    </button>
                                    <button class="btn btn-secondary" onclick="reportsModule.printReport()">
                                        <i class="fas fa-print me-2"></i>طباعة
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="btn-group" role="group">
                                    <button class="btn btn-success" id="loadDemoDataBtn">
                                        <i class="fas fa-database me-2"></i>تحميل بيانات تجريبية
                                    </button>
                                    <button class="btn btn-danger" id="resetDataBtn">
                                        <i class="fas fa-trash-alt me-2"></i>مسح جميع البيانات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row g-4 mb-4">
                    <div class="col-md-3">
                        <div class="card stat-card bg-primary text-white">
                            <div class="card-body">
                                <h6 class="card-title">إجمالي المبيعات</h6>
                                <h3 class="mb-0" id="totalSales">0.00</h3>
                                <small class="text-white-50">ريال سعودي</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-success text-white">
                            <div class="card-body">
                                <h6 class="card-title">عدد المعاملات</h6>
                                <h3 class="mb-0" id="transactionCount">0</h3>
                                <small class="text-white-50">معاملة</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-warning text-white">
                            <div class="card-body">
                                <h6 class="card-title">الوصفات الطبية</h6>
                                <h3 class="mb-0" id="prescriptionCount">0</h3>
                                <small class="text-white-50">وصفة</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-info text-white">
                            <div class="card-body">
                                <h6 class="card-title">المنتجات منخفضة المخزون</h6>
                                <h3 class="mb-0" id="lowStockCount">0</h3>
                                <small class="text-white-50">منتج</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="row g-4 mb-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">المبيعات اليومية</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="salesChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">توزيع المبيعات حسب الفئة</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="categoryChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Sales Table -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">آخر المبيعات</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم العملية</th>
                                        <th>التاريخ</th>
                                        <th>المنتجات</th>
                                        <th>المبلغ</th>
                                    </tr>
                                </thead>
                                <tbody id="recentSalesTable">
                                    <!-- Recent sales will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                        <div id="loadingMessage" class="text-center text-muted my-5" style="display: none;">
                            <i class="fas fa-spinner fa-spin fa-3x mb-3"></i>
                            <p>جاري تحميل البيانات...</p>
                        </div>
                        <div id="noSalesMessage" class="no-data-message" style="display: none;">
                            <i class="fas fa-shopping-cart"></i>
                            <h5>لا توجد مبيعات لعرضها حالياً</h5>
                            <p>ستظهر المبيعات هنا بمجرد إضافتها من نقطة البيع</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Use CDN for Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- SweetAlert2 for beautiful alerts -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Reports JavaScript -->
    <script src="reports.js"></script>
</body>
</html>