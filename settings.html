<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automata Pharmacy - الإعدادات</title>
    <link rel="stylesheet" href="./node_modules/bootstrap/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="./node_modules/@fortawesome/fontawesome-free/css/all.min.css">
    <style>
        body { background-color: #f8f9fa; }
        .sidebar { min-height: 100vh; background-color: #343a40; }
        .sidebar .nav-link { color: #fff; }
        .sidebar .nav-link:hover { background-color: #495057; }
        .main-content { padding: 20px; }
        .card { box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar p-0">
                <div class="text-center py-4">
                    <i class="fas fa-clinic-medical fa-3x text-white"></i>
                    <h5 class="text-white mt-2">Automata Pharmacy</h5>
                </div>
                <nav class="nav flex-column mt-3">
                    <a class="nav-link" href="index.html"><i class="fas fa-home me-2"></i>الرئيسية</a>
                    <a class="nav-link" href="index.html"><i class="fas fa-shopping-cart me-2"></i>نقطة البيع</a>
                    <a class="nav-link" href="inventory.html"><i class="fas fa-boxes me-2"></i>المخزون</a>
                    <a class="nav-link" href="prescriptions.html"><i class="fas fa-file-medical me-2"></i>الوصفات الطبية</a>
                    <a class="nav-link" href="reports.html"><i class="fas fa-chart-bar me-2"></i>التقارير</a>
                    <a class="nav-link active" href="settings.html"><i class="fas fa-cog me-2"></i>الإعدادات</a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 main-content">
                <h2 class="mb-4">إعدادات النظام</h2>

                <!-- Settings Sections -->
                <div class="row g-4">
                    <!-- Business Information -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">معلومات الصيدلية</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-4 text-center">
                                    <label class="form-label d-block">شعار الصيدلية</label>
                                    <div class="logo-preview mb-2">
                                        <img id="logoPreview" src="assets/default-logo.png" alt="شعار الصيدلية" class="img-thumbnail" style="max-height: 100px; max-width: 200px;">
                                    </div>
                                    <div class="d-flex justify-content-center gap-2">
                                        <button class="btn btn-sm btn-outline-primary" id="selectLogoBtn">
                                            <i class="fas fa-image me-1"></i>اختيار صورة
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" id="removeLogoBtn">
                                            <i class="fas fa-trash me-1"></i>حذف
                                        </button>
                                    </div>
                                    <input type="file" id="logoInput" accept="image/*" style="display: none;">
                                    <input type="hidden" id="logoData">
                                </div>
                                <!-- اسم النظام الذي يظهر في أعلى الفاتورة (قابل للتغيير) -->
                                <div class="mb-3">
                                    <label class="form-label">اسم النظام</label>
                                    <input type="text" class="form-control" id="systemName" placeholder="Automata Pharmacy">
                                    <small class="form-text text-muted">الاسم الذي سيظهر في أعلى الفاتورة</small>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">اسم الصيدلية</label>
                                    <input type="text" class="form-control" id="pharmacyName">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" id="pharmacyAddress" rows="2"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="pharmacyPhone">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="pharmacyEmail">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الرقم الضريبي</label>
                                    <input type="text" class="form-control" id="taxNumber">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- System Settings -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">إعدادات النظام</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">نسبة الضريبة (%)</label>
                                    <input type="number" class="form-control" id="taxRate">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العملة</label>
                                    <select class="form-select" id="currency">
                                        <!-- العملات العربية -->
                                        <option value="SAR">ريال سعودي (SAR)</option>
                                        <option value="IQD">دينار عراقي (IQD)</option>
                                        <option value="AED">درهم إماراتي (AED)</option>
                                        <option value="KWD">دينار كويتي (KWD)</option>
                                        <option value="EGP">جنيه مصري (EGP)</option>
                                        <option value="QAR">ريال قطري (QAR)</option>
                                        <option value="BHD">دينار بحريني (BHD)</option>
                                        <option value="OMR">ريال عماني (OMR)</option>
                                        <option value="JOD">دينار أردني (JOD)</option>
                                        <option value="LBP">ليرة لبنانية (LBP)</option>
                                        <option value="MAD">درهم مغربي (MAD)</option>
                                        <option value="TND">دينار تونسي (TND)</option>
                                        <option value="DZD">دينار جزائري (DZD)</option>
                                        <option value="LYD">دينار ليبي (LYD)</option>
                                        <!-- العملات العالمية -->
                                        <option value="USD">دولار أمريكي (USD)</option>
                                        <option value="EUR">يورو (EUR)</option>
                                        <option value="GBP">جنيه إسترليني (GBP)</option>
                                        <option value="JPY">ين ياباني (JPY)</option>
                                        <option value="CNY">يوان صيني (CNY)</option>
                                        <option value="CHF">فرنك سويسري (CHF)</option>
                                        <option value="CAD">دولار كندي (CAD)</option>
                                        <option value="AUD">دولار أسترالي (AUD)</option>
                                        <option value="INR">روبية هندية (INR)</option>
                                        <option value="TRY">ليرة تركية (TRY)</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">تنبيه المخزون المنخفض عند</label>
                                    <input type="number" class="form-control" id="lowStockAlert">
                                </div>
                                <!-- إعدادات الطباعة -->
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">إعدادات الطباعة</h6>
                                    </div>
                                    <div class="card-body">
                                        <!-- إعدادات الطابعات -->
                                        <div class="mb-3">
                                            <label class="form-label">الطابعة الافتراضية</label>
                                            <div class="input-group mb-2">
                                                <select class="form-select" id="defaultPrinter">
                                                    <option value="">-- اختر الطابعة --</option>
                                                    <!-- سيتم ملء هذه القائمة بالطابعات المتاحة -->
                                                </select>
                                                <button class="btn btn-outline-secondary" type="button" id="refreshPrinters">
                                                    <i class="fas fa-sync-alt"></i>
                                                </button>
                                            </div>
                                            <small class="form-text text-muted">الطابعة التي سيتم استخدامها لطباعة الإيصالات والفواتير</small>
                                        </div>

                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="silentPrint">
                                            <label class="form-check-label">تفعيل الطباعة الصامتة</label>
                                            <small class="form-text text-muted d-block">طباعة الإيصال تلقائياً بعد إتمام عملية البيع دون الحاجة إلى تأكيد</small>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">عدد نسخ الإيصال</label>
                                                    <select class="form-select" id="receiptCopies">
                                                        <option value="1">1</option>
                                                        <option value="2">2</option>
                                                        <option value="3">3</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">حجم الورق</label>
                                                    <select class="form-select" id="paperSize">
                                                        <option value="80mm">80مم (طابعة حرارية)</option>
                                                        <option value="a4">A4 (طابعة عادية)</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- إعدادات الطابعة الحرارية -->
                                        <div class="card mt-3 mb-3">
                                            <div class="card-header bg-light">
                                                <h6 class="mb-0">إعدادات الطابعة الحرارية</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="form-check form-switch mb-3">
                                                    <input class="form-check-input" type="checkbox" id="useThermalPrinter">
                                                    <label class="form-check-label">استخدام الطابعة الحرارية</label>
                                                    <small class="form-text text-muted d-block">استخدام الطابعة الحرارية بدلاً من طباعة الويندوز العادية</small>
                                                </div>

                                                <div class="mb-3">
                                                    <label class="form-label">نوع الطابعة الحرارية</label>
                                                    <select class="form-select" id="thermalPrinterType">
                                                        <option value="epson">Epson</option>
                                                        <option value="star">Star</option>
                                                    </select>
                                                </div>

                                                <div class="mb-3">
                                                    <label class="form-label">واجهة الطابعة الحرارية</label>
                                                    <input type="text" class="form-control" id="thermalPrinterInterface" placeholder="COM1 أو USB أو TCP://192.168.1.100">
                                                    <small class="form-text text-muted">مثال: COM1 للطابعة المتصلة بالمنفذ التسلسلي، أو TCP://192.168.1.100 للطابعة الشبكية</small>
                                                </div>

                                                <div class="mb-3">
                                                    <label class="form-label">عرض الطباعة (عدد الأحرف)</label>
                                                    <input type="number" class="form-control" id="thermalPrinterWidth" value="42">
                                                </div>

                                                <button class="btn btn-sm btn-outline-primary" id="testThermalPrint">
                                                    <i class="fas fa-print me-2"></i>اختبار الطابعة الحرارية
                                                </button>
                                            </div>
                                        </div>

                                        <div class="d-flex gap-2">
                                            <button class="btn btn-sm btn-outline-primary" id="testPrint">
                                                <i class="fas fa-print me-2"></i>اختبار الطباعة
                                            </button>
                                            <button class="btn btn-sm btn-outline-info" id="showPrinterInfo">
                                                <i class="fas fa-info-circle me-2"></i>معلومات الطابعات
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Backup Settings -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">النسخ الاحتياطي</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">مسار النسخ الاحتياطي</label>
                                    <input type="text" class="form-control" id="backupPath">
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="autoBackup">
                                    <label class="form-check-label">تفعيل النسخ الاحتياطي التلقائي</label>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">تكرار النسخ الاحتياطي</label>
                                    <select class="form-select" id="backupFrequency">
                                        <option value="daily">يومياً</option>
                                        <option value="weekly">أسبوعياً</option>
                                        <option value="monthly">شهرياً</option>
                                    </select>
                                </div>
                                <button class="btn btn-primary" disabled><i class="fas fa-download me-2"></i>نسخ احتياطي الآن</button>
                            </div>
                        </div>
                    </div>

                    <!-- User Interface Settings -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">إعدادات الواجهة</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">حجم الخط</label>
                                    <select class="form-select" id="fontSize">
                                        <option value="small">صغير</option>
                                        <option value="medium">متوسط</option>
                                        <option value="large">كبير</option>
                                    </select>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="darkMode">
                                    <label class="form-check-label">الوضع الليلي</label>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="notifications">
                                    <label class="form-check-label">تفعيل الإشعارات</label>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="fullscreenMode">
                                    <label class="form-check-label">وضع ملء الشاشة</label>
                                    <small class="form-text text-muted d-block">تشغيل البرنامج في وضع ملء الشاشة عند بدء التشغيل</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="mt-4">
                    <button class="btn btn-success" disabled><i class="fas fa-save me-2"></i>حفظ الإعدادات</button>
                </div>
            </div>
        </div>
    </div>

    <script src="./node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>