<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automata Pharmacy - الوصفات الطبية</title>
    <link rel="stylesheet" href="node_modules/bootstrap/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="node_modules/@fortawesome/fontawesome-free/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body { background-color: #f8f9fa; }
        .sidebar { min-height: 100vh; background-color: #343a40; }
        .sidebar .nav-link { color: #fff; }
        .sidebar .nav-link:hover { background-color: #495057; }
        .main-content { padding: 20px; }
        .card { box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }

        /* SweetAlert2 RTL fixes */
        .swal2-rtl {
            direction: rtl !important;
            text-align: right !important;
        }
        .swal2-rtl .swal2-title,
        .swal2-rtl .swal2-content,
        .swal2-rtl .swal2-html-container {
            text-align: right !important;
        }
        .swal2-rtl .swal2-actions {
            justify-content: flex-start !important;
        }

        /* تحسينات إضافية للجدول */
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }

        .badge {
            font-size: 0.75rem;
        }

        .btn-group-sm > .btn {
            padding: 0.25rem 0.4rem;
            font-size: 0.8rem;
        }

        /* تحسينات القائمة المنسدلة */
        .dropdown-menu {
            min-width: 280px;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border: 1px solid #e0e0e0;
            padding: 0.5rem 0;
            animation: dropdownFadeIn 0.3s ease-out;
        }

        @keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dropdown-item {
            padding: 1rem 1.5rem;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            border-radius: 0;
            display: flex;
            align-items: flex-start;
            flex-direction: column;
            line-height: 1.4;
        }

        .dropdown-item > span {
            display: flex;
            align-items: center;
            width: 100%;
            font-weight: 500;
        }

        .dropdown-item small {
            margin-top: 0.2rem;
            margin-right: 26px;
            font-size: 0.8rem;
            line-height: 1.2;
        }

        .dropdown-item:hover {
            background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
            transform: translateX(-3px);
            padding-right: 1.8rem;
        }

        .dropdown-item i {
            width: 24px;
            height: 24px;
            text-align: center;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .dropdown-item.disabled {
            opacity: 0.4;
            pointer-events: none;
            background-color: #f8f9fa;
        }

        .dropdown-item.text-danger:hover {
            background: linear-gradient(90deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
        }

        .dropdown-divider {
            margin: 0.5rem 0;
            border-top: 1px solid #dee2e6;
        }

        .dropdown-toggle {
            font-weight: 500;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .dropdown-toggle:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .dropdown-toggle::after {
            margin-right: 0.5rem;
            transition: transform 0.2s ease;
        }

        .dropdown-toggle[aria-expanded="true"]::after {
            transform: rotate(180deg);
        }

        /* تأثيرات خاصة للأيقونات */
        .dropdown-item .text-primary {
            color: #0d6efd !important;
        }

        .dropdown-item .text-warning {
            color: #fd7e14 !important;
        }

        .dropdown-item .text-info {
            color: #0dcaf0 !important;
        }

        .dropdown-item .text-success {
            color: #198754 !important;
        }

        .dropdown-item .text-secondary {
            color: #6c757d !important;
        }

        /* تحسينات العنوان */
        .dropdown-header {
            padding: 0.8rem 1.5rem 0.5rem;
            font-size: 0.9rem;
            font-weight: 600;
            color: #495057;
            background: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);
            border-bottom: 1px solid #e9ecef;
            margin-bottom: 0.5rem;
        }

        .dropdown-header i {
            font-size: 1rem;
        }

        /* تحسين الفواصل */
        .dropdown-divider {
            margin: 0.75rem 1rem;
            border-top: 1px solid #dee2e6;
            opacity: 0.7;
        }

        /* تحسينات نافذة الإجراءات المنبثقة */
        #actionsModal .modal-content {
            border: none;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        #actionsModal .modal-header {
            border-bottom: none;
            padding: 1.5rem;
        }

        #actionsModal .modal-body {
            padding: 0;
        }

        #actionsModal .list-group-item {
            border: none;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        #actionsModal .list-group-item:hover:not(.disabled) {
            background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
            transform: translateX(-5px);
            padding-right: 2rem !important;
        }

        #actionsModal .list-group-item:last-child {
            border-bottom: none;
        }

        #actionsModal .list-group-item.disabled {
            background-color: #f8f9fa;
            cursor: not-allowed;
        }

        #actionsModal .list-group-item.text-danger:hover {
            background: linear-gradient(90deg, #f8d7da 0%, #f5c6cb 100%);
        }

        /* دوائر الأيقونات */
        .icon-circle {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        /* تحسين النص */
        #actionsModal h6 {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }

        #actionsModal small {
            color: #6c757d;
            line-height: 1.3;
        }

        /* تحسين الفوتر */
        #actionsModal .modal-footer {
            border-top: 1px solid #e9ecef;
            padding: 1rem 1.5rem;
            background: #f8f9fa;
        }

        /* أنيميشن النافذة */
        #actionsModal.fade .modal-dialog {
            transition: transform 0.3s ease-out;
            transform: translate(0, -50px);
        }

        #actionsModal.show .modal-dialog {
            transform: none;
        }

        /* تحسين الأسهم */
        #actionsModal .fa-chevron-left {
            font-size: 0.9rem;
            transition: transform 0.3s ease;
        }

        #actionsModal .list-group-item:hover .fa-chevron-left {
            transform: translateX(-3px);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar p-0">
                <div class="text-center py-4">
                    <i class="fas fa-clinic-medical fa-3x text-white"></i>
                    <h5 class="text-white mt-2">Automata Pharmacy</h5>
                </div>
                <nav class="nav flex-column mt-3">
                    <a class="nav-link" href="index.html"><i class="fas fa-home me-2"></i>الرئيسية</a>
                    <a class="nav-link" href="index.html"><i class="fas fa-shopping-cart me-2"></i>نقطة البيع</a>
                    <a class="nav-link" href="inventory.html"><i class="fas fa-boxes me-2"></i>المخزون</a>
                    <a class="nav-link active" href="prescriptions.html"><i class="fas fa-file-medical me-2"></i>الوصفات الطبية</a>
                    <a class="nav-link" href="reports.html"><i class="fas fa-chart-bar me-2"></i>التقارير</a>
                    <a class="nav-link" href="settings.html"><i class="fas fa-cog me-2"></i>الإعدادات</a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>إدارة الوصفات الطبية</h2>
                    <button class="btn btn-primary" id="addPrescriptionBtn">
                        <i class="fas fa-plus me-2"></i>إضافة وصفة جديدة
                    </button>
                </div>

                <!-- Search and Filter -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <input type="text" class="form-control" placeholder="بحث عن وصفة..." id="searchPrescription">
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="statusFilter">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending">قيد الانتظار</option>
                                    <option value="completed">مكتملة</option>
                                    <option value="cancelled">ملغاة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <input type="date" class="form-control" id="dateFilter">
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                                    <i class="fas fa-times me-1"></i>مسح الفلاتر
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Prescriptions Table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الوصفة</th>
                                        <th>اسم المريض</th>
                                        <th>الطبيب</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="prescriptionItems">
                                    <!-- Items will be loaded dynamically -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Prescription Modal -->
    <div class="modal fade" id="addPrescriptionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة وصفة طبية جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addPrescriptionForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">اسم المريض</label>
                                <input type="text" class="form-control" name="patientName" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" name="phoneNumber">
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">اسم الطبيب</label>
                                <input type="text" class="form-control" name="doctorName" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">التاريخ</label>
                                <input type="date" class="form-control" name="date" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الأدوية</label>
                            <div id="medicationsList">
                                <div class="row mb-2">
                                    <div class="col-md-5">
                                        <input type="text" class="form-control" placeholder="اسم الدواء" name="medications[]" required>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="text" class="form-control" placeholder="الجرعة" name="dosage[]">
                                    </div>
                                    <div class="col-md-3">
                                        <input type="text" class="form-control" placeholder="المدة" name="duration[]">
                                    </div>
                                    <div class="col-md-1">
                                        <button type="button" class="btn btn-danger btn-sm" onclick="removeMedication(this)">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-secondary btn-sm mt-2" onclick="addMedication()">
                                <i class="fas fa-plus me-1"></i>إضافة دواء
                            </button>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="addPrescriptionForm" class="btn btn-primary">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <script src="node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="prescriptions.js"></script>
    <script>
        // دالة مسح الفلاتر
        function clearFilters() {
            document.getElementById('searchPrescription').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('dateFilter').value = '';
            filterPrescriptions();
        }
    </script>
</body>
</html>