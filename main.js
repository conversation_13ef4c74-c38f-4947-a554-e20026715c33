const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const Store = require('electron-store');

// إنشاء مخزن البيانات
const store = new Store();

let mainWindow;
let splashWindow;

// بيانات المنتجات والمبيعات والوصفات
let products = [];
let sales = [];
let cart = [];
let prescriptions = [];

// تهيئة البيانات الافتراضية
const defaultProducts = [
  { id: 1, code: '123456789012', name: 'باراسيتامول 500مج', price: 15.50, quantity: 100, category: 'medicine', expiryDate: '2025-12-31' },
  { id: 2, code: '123456789013', name: 'أسبرين 100مج', price: 12.00, quantity: 50, category: 'medicine', expiryDate: '2025-06-30' },
  { id: 3, code: '123456789014', name: 'فيتامين د3', price: 45.00, quantity: 30, category: 'medicine', expiryDate: '2026-03-15' },
  { id: 4, code: '123456789015', name: 'شاش طبي', price: 8.50, quantity: 200, category: 'supplies', expiryDate: '2027-01-01' },
  { id: 5, code: '123456789016', name: 'كريم مرطب', price: 25.00, quantity: 75, category: 'cosmetics', expiryDate: '2025-09-20' }
];

// بيانات المبيعات التجريبية
const defaultSales = [
  {
    id: 1703001234567,
    date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // منذ يومين
    timestamp: Date.now() - 2 * 24 * 60 * 60 * 1000,
    items: [
      { id: 1, name: 'باراسيتامول 500مج', code: '123456789012', price: 15.50, quantity: 2, category: 'medicine', total: 31.00 },
      { id: 4, name: 'شاش طبي', code: '123456789015', price: 8.50, quantity: 1, category: 'supplies', total: 8.50 }
    ],
    subtotal: 39.50,
    tax: 5.93,
    total: 45.43,
    paymentMethod: 'cash',
    shouldPrint: false
  },
  {
    id: 1703001234568,
    date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // منذ يوم
    timestamp: Date.now() - 1 * 24 * 60 * 60 * 1000,
    items: [
      { id: 3, name: 'فيتامين د3', code: '123456789014', price: 45.00, quantity: 1, category: 'medicine', total: 45.00 }
    ],
    subtotal: 45.00,
    tax: 6.75,
    total: 51.75,
    paymentMethod: 'card',
    shouldPrint: true
  },
  {
    id: 1703001234569,
    date: new Date().toISOString(), // اليوم
    timestamp: Date.now(),
    items: [
      { id: 2, name: 'أسبرين 100مج', code: '123456789013', price: 12.00, quantity: 3, category: 'medicine', total: 36.00 },
      { id: 5, name: 'كريم مرطب', code: '123456789016', price: 25.00, quantity: 1, category: 'cosmetics', total: 25.00 }
    ],
    subtotal: 61.00,
    tax: 9.15,
    total: 70.15,
    paymentMethod: 'cash',
    shouldPrint: false
  }
];

// بيانات الوصفات الافتراضية
const defaultPrescriptions = [
  {
    id: 1,
    patientName: 'أحمد محمد علي',
    phoneNumber: '**********',
    doctorName: 'د. سارة أحمد',
    date: '2024-06-06',
    medications: [
      { name: 'باراسيتامول 500مج', dosage: 'قرص واحد', duration: '3 مرات يومياً لمدة 5 أيام' },
      { name: 'أسبرين 100مج', dosage: 'قرص واحد', duration: 'مرة واحدة يومياً' }
    ],
    notes: 'يُنصح بتناول الدواء بعد الطعام',
    status: 'pending',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 2,
    patientName: 'فاطمة عبدالله',
    phoneNumber: '**********',
    doctorName: 'د. محمد حسن',
    date: '2024-06-05',
    medications: [
      { name: 'فيتامين د3', dosage: 'كبسولة واحدة', duration: 'مرة واحدة يومياً لمدة شهر' }
    ],
    notes: 'للمتابعة بعد شهر',
    status: 'completed',
    createdAt: new Date(Date.now() - 86400000).toISOString(),
    updatedAt: new Date().toISOString(),
    completedAt: new Date().toISOString()
  }
];

// تحميل البيانات المحفوظة أو استخدام البيانات الافتراضية
function loadData() {
  try {
    // تحميل المنتجات
    const savedProducts = store.get('products');
    if (savedProducts && savedProducts.length > 0) {
      products = savedProducts;
      console.log(`تم تحميل ${products.length} منتج من البيانات المحفوظة`);
    } else {
      products = [...defaultProducts];
      store.set('products', products);
      console.log('تم تحميل البيانات الافتراضية للمنتجات');
    }

    // تحميل المبيعات - إجبار استخدام البيانات التجريبية للاختبار
    sales = [...defaultSales];
    store.set('sales', sales);
    console.log(`✅ تم تحميل ${sales.length} عملية بيع تجريبية بقيمة إجمالية: ${sales.reduce((sum, sale) => sum + sale.total, 0).toFixed(2)} ر.س`);

    // تحميل الوصفات الطبية - إجبار استخدام البيانات التجريبية للاختبار
    prescriptions = [...defaultPrescriptions];
    store.set('prescriptions', prescriptions);
    console.log(`✅ تم تحميل ${prescriptions.length} وصفة طبية تجريبية`);

    // تحميل آخر معرف منتج ووصفة
    const lastProductId = store.get('lastProductId', 5);
    const lastPrescriptionId = store.get('lastPrescriptionId', 2);
    global.lastProductId = lastProductId;
    global.lastPrescriptionId = lastPrescriptionId;

  } catch (error) {
    console.error('خطأ في تحميل البيانات:', error);
    // في حالة الخطأ، استخدم البيانات الافتراضية
    products = [...defaultProducts];
    sales = [...defaultSales];
    prescriptions = [...defaultPrescriptions];
    global.lastProductId = 5;
    global.lastPrescriptionId = 2;
  }
}

// حفظ البيانات
function saveData() {
  try {
    store.set('products', products);
    store.set('sales', sales);
    store.set('prescriptions', prescriptions);
    store.set('lastProductId', global.lastProductId || 5);
    store.set('lastPrescriptionId', global.lastPrescriptionId || 0);
    console.log('تم حفظ جميع البيانات بنجاح');
  } catch (error) {
    console.error('خطأ في حفظ البيانات:', error);
  }
}

function createSplashWindow() {
  splashWindow = new BrowserWindow({
    width: 700,
    height: 500,
    frame: false,
    alwaysOnTop: true,
    transparent: true,
    center: true,
    resizable: false,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  splashWindow.loadFile('splash.html');

  // إغلاق splash screen بعد 4 ثوانٍ وإظهار النافذة الرئيسية
  setTimeout(() => {
    createMainWindow();
    if (splashWindow) {
      splashWindow.close();
      splashWindow = null;
    }
  }, 4000);
}

function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    show: false, // لا تظهر النافذة فوراً
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  mainWindow.loadFile('index.html');

  // إظهار النافذة الرئيسية مع تأثير fade in
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    mainWindow.focus();
  });

  // حفظ البيانات عند إغلاق النافذة
  mainWindow.on('close', () => {
    console.log('جاري حفظ البيانات قبل الإغلاق...');
    saveData();
  });
}

app.whenReady().then(() => {
  // تحميل البيانات عند بدء التطبيق
  loadData();
  createSplashWindow();
});

app.on('window-all-closed', () => {
  // حفظ البيانات قبل إغلاق التطبيق
  saveData();
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createSplashWindow();
  }
});

// حفظ البيانات بشكل دوري كل 30 ثانية
setInterval(() => {
  saveData();
}, 30000);

// IPC handlers لنقطة البيع
ipcMain.handle('get-product-by-barcode', async (event, barcode) => {
  const product = products.find(p => p.code === barcode);
  if (!product) {
    throw new Error('المنتج غير موجود');
  }
  if (product.quantity <= 0) {
    throw new Error('المنتج غير متوفر في المخزون');
  }
  return product;
});

ipcMain.handle('search-products', async (event, searchTerm) => {
  if (!searchTerm || searchTerm.length < 2) return [];

  return products.filter(p =>
    p.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    p.code.includes(searchTerm)
  );
});

ipcMain.handle('add-to-cart', async (event, product) => {
  const existingItem = cart.find(item => item.id === product.id);

  if (existingItem) {
    if (existingItem.quantity >= product.quantity) {
      throw new Error('لا يوجد مخزون كافي');
    }
    existingItem.quantity += 1;
  } else {
    cart.push({ ...product, quantity: 1 });
  }

  return cart;
});

ipcMain.handle('update-cart-quantity', async (event, productId, newQuantity) => {
  const item = cart.find(item => item.id === productId);
  if (item) {
    const product = products.find(p => p.id === productId);
    if (newQuantity > product.quantity) {
      throw new Error('لا يوجد مخزون كافي');
    }
    if (newQuantity <= 0) {
      cart = cart.filter(item => item.id !== productId);
    } else {
      item.quantity = newQuantity;
    }
  }
  return cart;
});

ipcMain.handle('remove-from-cart', async (event, productId) => {
  cart = cart.filter(item => item.id !== productId);
  return cart;
});

ipcMain.handle('get-cart', async () => {
  return cart;
});

ipcMain.handle('clear-cart', async () => {
  cart = [];
  return cart;
});

ipcMain.handle('process-sale', async (event, saleData) => {
  try {
    // إنشاء معرف فريد للعملية
    const saleId = Date.now();

    // إنشاء سجل البيع
    const saleRecord = {
      id: saleId,
      date: new Date().toISOString(),
      items: cart.map(item => ({
        id: item.id,
        name: item.name,
        code: item.code,
        price: item.price,
        quantity: item.quantity,
        category: item.category,
        total: item.price * item.quantity
      })),
      subtotal: saleData.subtotal,
      tax: saleData.tax,
      total: saleData.total,
      paymentMethod: saleData.paymentMethod,
      shouldPrint: saleData.shouldPrint || false
    };

    // حفظ البيع في قائمة المبيعات
    sales.push(saleRecord);

    // تحديث المخزون
    for (const item of cart) {
      const product = products.find(p => p.id === item.id);
      if (product) {
        product.quantity -= item.quantity;
        product.updatedAt = new Date().toISOString();
      }
    }

    // مسح السلة
    cart = [];

    // حفظ البيانات فوراً
    saveData();

    console.log(`تم حفظ عملية البيع رقم ${saleId} بمبلغ ${saleData.total} ريال`);

    // إرسال إشعار تحديث للتقارير
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      window.webContents.send('sale-completed', {
        saleId: saleId,
        saleRecord: saleRecord,
        timestamp: new Date().toISOString()
      });
    });

    return { success: true, saleId: saleId, saleRecord: saleRecord };
  } catch (error) {
    console.error('خطأ في معالجة البيع:', error);
    return { success: false, error: error.message };
  }
});

// IPC handlers لإدارة المخزون
ipcMain.handle('get-inventory', async () => {
  return products;
});

ipcMain.handle('add-product', async (event, productData) => {
  // التحقق من عدم تكرار الكود
  const existingProduct = products.find(p => p.code === productData.code);
  if (existingProduct) {
    throw new Error('هذا الكود موجود بالفعل');
  }

  // إنشاء معرف فريد جديد
  global.lastProductId = (global.lastProductId || 5) + 1;

  const newProduct = {
    id: global.lastProductId,
    code: productData.code,
    name: productData.name,
    category: productData.category,
    price: parseFloat(productData.price),
    quantity: parseInt(productData.quantity),
    expiryDate: productData.expiryDate,
    createdAt: new Date().toISOString()
  };

  products.push(newProduct);

  // حفظ البيانات فوراً
  saveData();

  return newProduct;
});

ipcMain.handle('update-product', async (event, productData) => {
  const index = products.findIndex(p => p.id === productData.id);
  if (index === -1) {
    throw new Error('المنتج غير موجود');
  }

  // التحقق من عدم تكرار الكود (إلا إذا كان نفس المنتج)
  const existingProduct = products.find(p => p.code === productData.code && p.id !== productData.id);
  if (existingProduct) {
    throw new Error('هذا الكود موجود بالفعل');
  }

  products[index] = {
    ...products[index],
    code: productData.code,
    name: productData.name,
    category: productData.category,
    price: parseFloat(productData.price),
    quantity: parseInt(productData.quantity),
    expiryDate: productData.expiryDate,
    updatedAt: new Date().toISOString()
  };

  // حفظ البيانات فوراً
  saveData();

  return products[index];
});

ipcMain.handle('delete-product', async (event, productId) => {
  const index = products.findIndex(p => p.id === productId);
  if (index === -1) {
    throw new Error('المنتج غير موجود');
  }

  products.splice(index, 1);

  // حفظ البيانات فوراً
  saveData();

  return true;
});

ipcMain.handle('search-inventory', async (event, searchTerm) => {
  if (!searchTerm || searchTerm.length < 1) return products;

  return products.filter(p =>
    p.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    p.code.includes(searchTerm)
  );
});

ipcMain.handle('filter-inventory', async (event, filters) => {
  let filteredProducts = [...products];

  // فلترة حسب الفئة
  if (filters.category) {
    filteredProducts = filteredProducts.filter(p => p.category === filters.category);
  }

  // فلترة حسب حالة المخزون
  if (filters.stockStatus) {
    switch (filters.stockStatus) {
      case 'out':
        filteredProducts = filteredProducts.filter(p => p.quantity === 0);
        break;
      case 'low':
        filteredProducts = filteredProducts.filter(p => p.quantity > 0 && p.quantity < 10);
        break;
      case 'available':
        filteredProducts = filteredProducts.filter(p => p.quantity >= 10);
        break;
    }
  }

  return filteredProducts;
});

ipcMain.handle('quick-add-stock', async (event, barcode, quantity) => {
  const product = products.find(p => p.code === barcode);
  if (!product) {
    throw new Error('المنتج غير موجود');
  }

  product.quantity += parseInt(quantity);
  product.updatedAt = new Date().toISOString();

  // حفظ البيانات فوراً
  saveData();

  return product;
});

// دوال إضافية للمبيعات والإحصائيات
ipcMain.handle('get-sales', async (event, dateRange) => {
  try {
    let filteredSales = [...sales];

    // فلترة حسب التاريخ إذا تم تحديده
    if (dateRange && dateRange.start && dateRange.end) {
      const startDate = new Date(dateRange.start);
      const endDate = new Date(dateRange.end);
      endDate.setHours(23, 59, 59, 999);

      filteredSales = sales.filter(sale => {
        const saleDate = new Date(sale.timestamp || sale.date);
        return saleDate >= startDate && saleDate <= endDate;
      });
    }

    return filteredSales;
  } catch (error) {
    console.error('خطأ في جلب المبيعات:', error);
    return [];
  }
});

ipcMain.handle('get-sales-statistics', async () => {
  try {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    // مبيعات اليوم
    const todaySales = sales.filter(sale => {
      const saleDate = new Date(sale.timestamp || sale.date);
      return saleDate >= startOfDay;
    });

    // مبيعات الشهر
    const monthSales = sales.filter(sale => {
      const saleDate = new Date(sale.timestamp || sale.date);
      return saleDate >= startOfMonth;
    });

    // إجمالي المبيعات
    const totalSales = sales.reduce((sum, sale) => sum + sale.total, 0);
    const todayTotal = todaySales.reduce((sum, sale) => sum + sale.total, 0);
    const monthTotal = monthSales.reduce((sum, sale) => sum + sale.total, 0);

    // عدد المنتجات المباعة
    const totalItemsSold = sales.reduce((sum, sale) => {
      return sum + sale.items.reduce((itemSum, item) => itemSum + item.quantity, 0);
    }, 0);

    // المنتجات الأكثر مبيعاً
    const productSales = {};
    sales.forEach(sale => {
      sale.items.forEach(item => {
        if (!productSales[item.id]) {
          productSales[item.id] = {
            id: item.id,
            name: item.name,
            totalQuantity: 0,
            totalRevenue: 0
          };
        }
        productSales[item.id].totalQuantity += item.quantity;
        productSales[item.id].totalRevenue += item.total;
      });
    });

    const topProducts = Object.values(productSales)
      .sort((a, b) => b.totalQuantity - a.totalQuantity)
      .slice(0, 5);

    return {
      totalSales: sales.length,
      totalRevenue: totalSales,
      todaySales: todaySales.length,
      todayRevenue: todayTotal,
      monthSales: monthSales.length,
      monthRevenue: monthTotal,
      totalItemsSold,
      topProducts,
      lowStockProducts: products.filter(p => p.quantity < 10).length
    };
  } catch (error) {
    console.error('خطأ في حساب الإحصائيات:', error);
    return {
      totalSales: 0,
      totalRevenue: 0,
      todaySales: 0,
      todayRevenue: 0,
      monthSales: 0,
      monthRevenue: 0,
      totalItemsSold: 0,
      topProducts: [],
      lowStockProducts: 0
    };
  }
});

// دالة لإعادة تعيين البيانات (للاختبار)
ipcMain.handle('reset-all-data', async () => {
  try {
    products = [...defaultProducts];
    sales = [...defaultSales];
    prescriptions = [...defaultPrescriptions];
    cart = [];
    global.lastProductId = 5;
    global.lastPrescriptionId = 2;

    // حفظ البيانات المعاد تعيينها
    saveData();

    console.log('✅ تم إعادة تعيين جميع البيانات');
    console.log(`📊 البيانات الجديدة: ${products.length} منتج، ${sales.length} مبيعة، ${prescriptions.length} وصفة`);
    return { success: true, message: 'تم إعادة تعيين جميع البيانات بنجاح' };
  } catch (error) {
    console.error('❌ خطأ في إعادة تعيين البيانات:', error);
    return { success: false, error: error.message };
  }
});

// دالة لإجبار تحميل البيانات التجريبية
ipcMain.handle('force-load-demo-data', async () => {
  try {
    console.log('🔄 إجبار تحميل البيانات التجريبية...');

    // إجبار تحميل البيانات التجريبية
    products = [...defaultProducts];
    sales = [...defaultSales];
    prescriptions = [...defaultPrescriptions];

    // حفظ البيانات
    store.set('products', products);
    store.set('sales', sales);
    store.set('prescriptions', prescriptions);

    console.log('✅ تم تحميل البيانات التجريبية بنجاح');
    console.log(`📊 المبيعات: ${sales.length} عملية بقيمة ${sales.reduce((sum, sale) => sum + sale.total, 0).toFixed(2)} ر.س`);
    console.log(`💊 الوصفات: ${prescriptions.length} وصفة طبية`);

    // إرسال إشعار تحديث لجميع النوافذ
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      window.webContents.send('data-force-updated', {
        sales: sales.length,
        prescriptions: prescriptions.length,
        products: products.length
      });
    });

    return {
      success: true,
      data: {
        sales: sales.length,
        prescriptions: prescriptions.length,
        products: products.length,
        totalSales: sales.reduce((sum, sale) => sum + sale.total, 0)
      }
    };
  } catch (error) {
    console.error('❌ خطأ في تحميل البيانات التجريبية:', error);
    return { success: false, error: error.message };
  }
});

// دالة للنسخ الاحتياطي
ipcMain.handle('backup-data', async () => {
  try {
    const backupData = {
      products,
      sales,
      prescriptions,
      lastProductId: global.lastProductId,
      lastPrescriptionId: global.lastPrescriptionId,
      backupDate: new Date().toISOString(),
      version: '1.0'
    };

    return { success: true, data: backupData };
  } catch (error) {
    console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
    return { success: false, error: error.message };
  }
});

// IPC handlers للوصفات الطبية
ipcMain.handle('get-prescriptions', async (event, filters) => {
  try {
    let filteredPrescriptions = [...prescriptions];

    // فلترة حسب التاريخ
    if (filters && filters.dateFrom && filters.dateTo) {
      const fromDate = new Date(filters.dateFrom);
      const toDate = new Date(filters.dateTo);
      toDate.setHours(23, 59, 59, 999);

      filteredPrescriptions = filteredPrescriptions.filter(prescription => {
        const prescriptionDate = new Date(prescription.date);
        return prescriptionDate >= fromDate && prescriptionDate <= toDate;
      });
    }

    // فلترة حسب الحالة
    if (filters && filters.status) {
      filteredPrescriptions = filteredPrescriptions.filter(p => p.status === filters.status);
    }

    // فلترة حسب البحث
    if (filters && filters.searchTerm) {
      const searchTerm = filters.searchTerm.toLowerCase();
      filteredPrescriptions = filteredPrescriptions.filter(p =>
        p.patientName.toLowerCase().includes(searchTerm) ||
        p.doctorName.toLowerCase().includes(searchTerm) ||
        p.id.toString().includes(searchTerm)
      );
    }

    return filteredPrescriptions.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
  } catch (error) {
    console.error('خطأ في جلب الوصفات:', error);
    return [];
  }
});

ipcMain.handle('add-prescription', async (event, prescriptionData) => {
  try {
    // إنشاء معرف فريد جديد
    global.lastPrescriptionId = (global.lastPrescriptionId || 0) + 1;

    const newPrescription = {
      id: global.lastPrescriptionId,
      patientName: prescriptionData.patientName,
      phoneNumber: prescriptionData.phoneNumber || '',
      doctorName: prescriptionData.doctorName,
      date: prescriptionData.date,
      medications: prescriptionData.medications || [],
      notes: prescriptionData.notes || '',
      status: 'pending',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    prescriptions.push(newPrescription);

    // حفظ البيانات فوراً
    saveData();

    console.log(`تم إضافة وصفة طبية جديدة رقم ${newPrescription.id} للمريض ${newPrescription.patientName}`);
    return newPrescription;
  } catch (error) {
    console.error('خطأ في إضافة الوصفة:', error);
    throw new Error('فشل في إضافة الوصفة الطبية');
  }
});

ipcMain.handle('update-prescription', async (event, prescriptionData) => {
  try {
    const index = prescriptions.findIndex(p => p.id === prescriptionData.id);
    if (index === -1) {
      throw new Error('الوصفة غير موجودة');
    }

    prescriptions[index] = {
      ...prescriptions[index],
      patientName: prescriptionData.patientName,
      phoneNumber: prescriptionData.phoneNumber || '',
      doctorName: prescriptionData.doctorName,
      date: prescriptionData.date,
      medications: prescriptionData.medications || [],
      notes: prescriptionData.notes || '',
      status: prescriptionData.status || prescriptions[index].status,
      updatedAt: new Date().toISOString()
    };

    // حفظ البيانات فوراً
    saveData();

    console.log(`تم تحديث الوصفة رقم ${prescriptionData.id}`);
    return prescriptions[index];
  } catch (error) {
    console.error('خطأ في تحديث الوصفة:', error);
    throw new Error(error.message || 'فشل في تحديث الوصفة');
  }
});

ipcMain.handle('delete-prescription', async (event, prescriptionId) => {
  try {
    const index = prescriptions.findIndex(p => p.id === prescriptionId);
    if (index === -1) {
      throw new Error('الوصفة غير موجودة');
    }

    const deletedPrescription = prescriptions.splice(index, 1)[0];

    // حفظ البيانات فوراً
    saveData();

    console.log(`تم حذف الوصفة رقم ${prescriptionId}`);
    return true;
  } catch (error) {
    console.error('خطأ في حذف الوصفة:', error);
    throw new Error(error.message || 'فشل في حذف الوصفة');
  }
});

ipcMain.handle('update-prescription-status', async (event, prescriptionId, newStatus) => {
  try {
    const prescription = prescriptions.find(p => p.id === prescriptionId);
    if (!prescription) {
      throw new Error('الوصفة غير موجودة');
    }

    prescription.status = newStatus;
    prescription.updatedAt = new Date().toISOString();

    // إذا تم إكمال الوصفة، أضف تاريخ الإكمال
    if (newStatus === 'completed') {
      prescription.completedAt = new Date().toISOString();
    }

    // حفظ البيانات فوراً
    saveData();

    console.log(`تم تحديث حالة الوصفة رقم ${prescriptionId} إلى ${newStatus}`);
    return prescription;
  } catch (error) {
    console.error('خطأ في تحديث حالة الوصفة:', error);
    throw new Error(error.message || 'فشل في تحديث حالة الوصفة');
  }
});

ipcMain.handle('get-prescription-by-id', async (event, prescriptionId) => {
  try {
    const prescription = prescriptions.find(p => p.id === prescriptionId);
    if (!prescription) {
      throw new Error('الوصفة غير موجودة');
    }
    return prescription;
  } catch (error) {
    console.error('خطأ في جلب الوصفة:', error);
    throw new Error(error.message || 'فشل في جلب الوصفة');
  }
});

// ربط الوصفة بنقطة البيع
ipcMain.handle('add-prescription-to-cart', async (event, prescriptionId) => {
  try {
    const prescription = prescriptions.find(p => p.id === prescriptionId);
    if (!prescription) {
      throw new Error('الوصفة غير موجودة');
    }

    let addedItems = 0;
    let unavailableItems = [];

    // إضافة كل دواء في الوصفة إلى السلة
    for (const medication of prescription.medications) {
      // البحث عن الدواء في المخزون
      const product = products.find(p =>
        p.name.toLowerCase().includes(medication.name.toLowerCase()) ||
        medication.name.toLowerCase().includes(p.name.toLowerCase())
      );

      if (product && product.quantity > 0) {
        // التحقق من وجود المنتج في السلة
        const existingItem = cart.find(item => item.id === product.id);

        if (existingItem) {
          if (existingItem.quantity < product.quantity) {
            existingItem.quantity += 1;
            addedItems++;
          } else {
            unavailableItems.push(`${medication.name} - مخزون غير كافي`);
          }
        } else {
          cart.push({ ...product, quantity: 1 });
          addedItems++;
        }
      } else {
        unavailableItems.push(`${medication.name} - غير متوفر في المخزون`);
      }
    }

    return {
      success: true,
      addedItems,
      unavailableItems,
      cart,
      message: `تم إضافة ${addedItems} عنصر إلى السلة`
    };

  } catch (error) {
    console.error('خطأ في إضافة الوصفة للسلة:', error);
    throw new Error(error.message || 'فشل في إضافة الوصفة للسلة');
  }
});

// دوال التقارير والتصدير
ipcMain.handle('get-products', async () => {
  return products;
});

ipcMain.handle('clear-sales', async () => {
  try {
    sales = [];
    saveData();
    return { success: true };
  } catch (error) {
    console.error('خطأ في مسح المبيعات:', error);
    throw new Error('فشل في مسح المبيعات');
  }
});

ipcMain.handle('clear-prescriptions', async () => {
  try {
    prescriptions = [];
    saveData();
    return { success: true };
  } catch (error) {
    console.error('خطأ في مسح الوصفات:', error);
    throw new Error('فشل في مسح الوصفات');
  }
});

ipcMain.handle('reset-inventory', async () => {
  try {
    // إعادة تعيين كميات المنتجات إلى القيم الافتراضية
    products.forEach(product => {
      product.quantity = 100; // كمية افتراضية
    });
    saveData();
    return { success: true };
  } catch (error) {
    console.error('خطأ في إعادة تعيين المخزون:', error);
    throw new Error('فشل في إعادة تعيين المخزون');
  }
});

ipcMain.handle('export-report-pdf', async (event, reportData) => {
  try {
    const { dialog } = require('electron');
    const fs = require('fs').promises;
    const path = require('path');

    // اختيار مكان الحفظ
    const result = await dialog.showSaveDialog({
      title: 'حفظ تقرير PDF',
      defaultPath: `تقرير_${new Date().toISOString().split('T')[0]}.pdf`,
      filters: [
        { name: 'PDF Files', extensions: ['pdf'] }
      ]
    });

    if (!result.canceled) {
      // هنا يمكن إضافة مكتبة لإنشاء PDF
      // للبساطة، سنحفظ البيانات كـ JSON مؤقتاً
      const jsonData = JSON.stringify(reportData, null, 2);
      await fs.writeFile(result.filePath.replace('.pdf', '.json'), jsonData, 'utf8');

      return { success: true, path: result.filePath };
    }

    return { success: false, message: 'تم إلغاء العملية' };
  } catch (error) {
    console.error('خطأ في تصدير PDF:', error);
    throw new Error('فشل في تصدير التقرير');
  }
});

ipcMain.handle('export-report-excel', async (event, reportData) => {
  try {
    const { dialog } = require('electron');
    const fs = require('fs').promises;

    // اختيار مكان الحفظ
    const result = await dialog.showSaveDialog({
      title: 'حفظ تقرير Excel',
      defaultPath: `تقرير_${new Date().toISOString().split('T')[0]}.xlsx`,
      filters: [
        { name: 'Excel Files', extensions: ['xlsx'] }
      ]
    });

    if (!result.canceled) {
      // تحويل البيانات إلى CSV للبساطة
      let csvContent = 'التاريخ,رقم العملية,المنتجات,المبلغ\n';

      reportData.sales.forEach(sale => {
        const date = new Date(sale.timestamp).toLocaleDateString('ar-SA');
        const products = sale.items.map(item => item.name || 'منتج').join('; ');
        csvContent += `${date},${sale.id},"${products}",${sale.total}\n`;
      });

      await fs.writeFile(result.filePath.replace('.xlsx', '.csv'), csvContent, 'utf8');

      return { success: true, path: result.filePath };
    }

    return { success: false, message: 'تم إلغاء العملية' };
  } catch (error) {
    console.error('خطأ في تصدير Excel:', error);
    throw new Error('فشل في تصدير التقرير');
  }
});


