# برنامج صيدلية كاشير - نقطة البيع مع الماسح الليزري

## نظرة عامة
تم تفعيل نقطة البيع مع دعم كامل للماسح الليزري والوظائف الأساسية للبيع.

## الوظائف المفعلة:

### 🛒 نقطة البيع
- **الماسح الليزري**: دعم كامل للماسح الليزري مع كشف تلقائي للباركود
- **البحث اليدوي**: البحث عن المنتجات بالاسم أو الكود
- **إدارة السلة**: إضافة/حذف/تعديل كمية المنتجات
- **حساب الضرائب**: حساب تلقائي للضريبة (15%)
- **طرق الدفع**: نقدي أو بطاقة ائتمان
- **إتمام البيع**: زرين منفصلين - عادي أو مع طباعة
- **طباعة الفواتير**: فواتير احترافية قابلة للطباعة

### 📦 إدارة المخزون
- **عرض شامل**: جدول تفاعلي لجميع المنتجات مع ألوان تحذيرية
- **الماسح الليزري**: بحث سريع عن المنتجات أو اقتراح إضافة منتجات جديدة
- **إضافة المنتجات**: نماذج سهلة لإضافة منتجات جديدة
- **تعديل المنتجات**: تحديث بيانات المنتجات الموجودة
- **الإضافة السريعة**: إضافة كميات للمخزون بسرعة
- **البحث والفلترة**: بحث متقدم وفلترة حسب الفئة وحالة المخزون
- **حذف المنتجات**: حذف آمن مع تأكيد

### 🔍 الماسح الليزري
- **كشف تلقائي**: يتعرف على الباركود من الماسح تلقائياً
- **إدخال يدوي**: يمكن كتابة الباركود يدوياً
- **اختبار الماسح**: زر لاختبار الماسح بباركود تجريبي
- **صوت تأكيد**: صوت عند نجاح المسح
- **إشعارات**: رسائل واضحة للنجاح أو الخطأ

### 📦 المنتجات المتاحة (بيانات تجريبية)
1. **باراسيتامول 500مج** - الكود: 123456789012 - السعر: 15.50 ريال
2. **أسبرين 100مج** - الكود: 123456789013 - السعر: 12.00 ريال
3. **فيتامين د3** - الكود: 123456789014 - السعر: 45.00 ريال
4. **شاش طبي** - الكود: 123456789015 - السعر: 8.50 ريال
5. **كريم مرطب** - الكود: 123456789016 - السعر: 25.00 ريال

## كيفية الاستخدام:

### 🖱️ استخدام الماسح الليزري
1. **المسح التلقائي**: وجه الماسح نحو الباركود وسيتم إضافة المنتج تلقائياً
2. **الإدخال اليدوي**: اكتب الباركود في حقل "ماسح الباركود" واضغط Enter
3. **اختبار الماسح**: اضغط الزر الأخضر لاختبار الماسح بباركود تجريبي

### 🔍 البحث عن المنتجات
- اكتب اسم المنتج في حقل البحث
- ستظهر النتائج تلقائياً
- اضغط على المنتج لإضافته للسلة

### 🛒 إدارة السلة
- **تغيير الكمية**: استخدم أزرار + و - أو اكتب الكمية مباشرة
- **حذف منتج**: اضغط زر سلة المهملات
- **مسح السلة**: اضغط زر "إلغاء"

### 💳 إتمام البيع
1. اختر طريقة الدفع (نقدي أو بطاقة)
2. اضغط "إتمام البيع" أو "إتمام البيع والطباعة"
3. ستظهر نافذة تأكيد العملية
4. سيتم تحديث المخزون تلقائياً

## 💊 إدارة الوصفات الطبية

### ✨ الميزات الجديدة:
- **إضافة وصفات طبية** مع تفاصيل المريض والطبيب
- **إدارة الأدوية** مع الجرعات والمدة
- **تتبع حالة الوصفة** (قيد الانتظار، مكتملة، ملغاة)
- **البحث والفلترة** المتقدمة
- **ربط مع نقطة البيع** لإضافة أدوية الوصفة للسلة

### 🔍 كيفية الاستخدام:
1. **إضافة وصفة جديدة**: اضغط "إضافة وصفة جديدة"
2. **ملء البيانات**: اسم المريض، الطبيب، التاريخ
3. **إضافة الأدوية**: اسم الدواء، الجرعة، المدة
4. **حفظ الوصفة**: اضغط "حفظ"

### 🛒 ربط مع نقطة البيع:
- **من الوصفات**: اضغط زر السلة لإضافة أدوية الوصفة
- **من نقطة البيع**: اضغط زر الوصفات للوصول السريع

## الصفحات المتاحة:
1. **index.html** - نقطة البيع (مفعلة) ✅
2. **inventory.html** - إدارة المخزون (مفعلة) ✅
3. **prescriptions.html** - الوصفات الطبية (مفعلة) ✅
4. **reports.html** - التقارير (تصميم فقط)
5. **settings.html** - الإعدادات (تصميم فقط)

## كيفية تشغيل البرنامج:
```bash
npm install
npm start
```

## ملاحظات:
- البرنامج الآن يعرض التصميم فقط بدون أي وظائف
- يمكنك البناء عليه وإضافة الوظائف التي تريدها
- التصميم متجاوب ويدعم اللغة العربية (RTL)
- جميع الأيقونات والألوان محفوظة

## الملفات الجديدة:
```
├── index.html          # نقطة البيع (مفعلة) ✅
├── pos.js             # وظائف نقطة البيع والماسح الليزري ✨
├── inventory.html      # صفحة المخزون (مفعلة) ✅
├── inventory.js       # وظائف إدارة المخزون والماسح الليزري ✨
├── main.js            # خادم Electron مع IPC handlers
├── prescriptions.html  # صفحة الوصفات الطبية (مفعلة) ✅
├── prescriptions.js    # وظائف إدارة الوصفات الطبية ✨
├── reports.html        # صفحة التقارير (تصميم فقط)
├── settings.html       # صفحة الإعدادات (تصميم فقط)
├── package.json       # المكتبات الأساسية
├── node_modules/      # مكتبات Bootstrap و FontAwesome
├── USAGE.md           # دليل استخدام نقطة البيع
├── INVENTORY_GUIDE.md # دليل إدارة المخزون
├── PRINTING_GUIDE.md  # دليل نظام الطباعة
├── DATA_PERSISTENCE_GUIDE.md # دليل حفظ البيانات
├── PRESCRIPTIONS_GUIDE.md # دليل نظام الوصفات الطبية
├── SPLASH_SCREEN_GUIDE.md # دليل شاشة البداية الديناميكية
└── assets/           # الصور والشعارات
```

## المميزات التقنية:
- **كشف الماسح التلقائي**: يميز بين الإدخال السريع (ماسح) والبطيء (يدوي)
- **حفظ البيانات الدائم**: جميع البيانات محفوظة تلقائياً ولا تختفي عند الإغلاق
- **إدارة الحالة المتقدمة**: تتبع السلة والمخزون والمبيعات
- **واجهة متجاوبة**: تعمل على جميع أحجام الشاشات
- **إشعارات فورية**: رسائل واضحة لكل عملية
- **أصوات تأكيد**: صوت عند نجاح العمليات
- **دعم العربية**: واجهة كاملة باللغة العربية (RTL)
- **نظام طباعة احترافي**: فواتير قابلة للطباعة
- **إحصائيات شاملة**: تقارير المبيعات والمخزون

## الخطوات التالية:
يمكنك الآن إضافة:
- قاعدة بيانات حقيقية (SQLite/MySQL)
- طباعة الفواتير والتقارير
- ~~إدارة المخزون~~ ✅ **مكتملة**
- التقارير والإحصائيات المتقدمة
- نظام المستخدمين والصلاحيات
- الوصفات الطبية
- النسخ الاحتياطي التلقائي

## 🎉 الوضع الحالي:
- ✅ **نقطة البيع**: مكتملة مع الماسح الليزري والطباعة
- ✅ **إدارة المخزون**: مكتملة مع جميع الوظائف
- ✅ **نظام الطباعة**: مكتمل مع فواتير احترافية
- ✅ **حفظ البيانات**: مكتمل مع حفظ دائم وآمن
- ✅ **الإحصائيات**: تتبع شامل للمبيعات والمخزون
- 🔄 **الوصفات الطبية**: قيد التطوير
- 🔄 **التقارير المتقدمة**: قيد التطوير
- 🔄 **الإعدادات**: قيد التطوير

## 💾 **مميزات حفظ البيانات:**
- **حفظ تلقائي**: بعد كل عملية مهمة
- **حفظ دوري**: كل 30 ثانية
- **حفظ عند الإغلاق**: ضمان عدم فقدان البيانات
- **استعادة تلقائية**: عند فتح التطبيق
- **نسخ احتياطي**: إمكانية تصدير واستيراد البيانات

البرنامج الآن جاهز للاستخدام التجاري الكامل مع ضمان حفظ البيانات! 🚀
