<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automata Pharmacy - المخزون</title>
    <link rel="stylesheet" href="./node_modules/bootstrap/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="./node_modules/@fortawesome/fontawesome-free/css/all.min.css">
    <!-- Use CDN for SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <!-- Fallback to CDN if local file fails -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <style>
        body { background-color: #f8f9fa; }
        .sidebar { min-height: 100vh; background-color: #343a40; }
        .sidebar .nav-link { color: #fff; }
        .sidebar .nav-link:hover { background-color: #495057; }
        .main-content { padding: 20px; }
        .card { box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }

        /* SweetAlert2 RTL fixes */
        .swal2-rtl {
            direction: rtl !important;
            text-align: right !important;
        }
        .swal2-rtl .swal2-title,
        .swal2-rtl .swal2-content,
        .swal2-rtl .swal2-html-container {
            text-align: right !important;
        }
        .swal2-rtl .swal2-actions {
            justify-content: flex-start !important;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar p-0">
                <div class="text-center py-4">
                    <i class="fas fa-clinic-medical fa-3x text-white"></i>
                    <h5 class="text-white mt-2">Automata Pharmacy</h5>
                </div>
                <nav class="nav flex-column mt-3">
                    <a class="nav-link" href="index.html"><i class="fas fa-home me-2"></i>الرئيسية</a>
                    <a class="nav-link" href="index.html"><i class="fas fa-shopping-cart me-2"></i>نقطة البيع</a>
                    <a class="nav-link active" href="inventory.html"><i class="fas fa-boxes me-2"></i>المخزون</a>
                    <a class="nav-link" href="prescriptions.html"><i class="fas fa-file-medical me-2"></i>الوصفات الطبية</a>
                    <a class="nav-link" href="reports.html"><i class="fas fa-chart-bar me-2"></i>التقارير</a>
                    <a class="nav-link" href="settings.html"><i class="fas fa-cog me-2"></i>الإعدادات</a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>إدارة المخزون</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                        <i class="fas fa-plus me-2"></i>إضافة منتج جديد
                    </button>
                </div>

                <!-- Barcode Scanner -->
                <div class="card mb-3 border-primary">
                    <div class="card-body p-3">
                        <h6 class="card-title mb-2"><i class="fas fa-barcode me-2"></i>ماسح الباركود</h6>
                        <div class="row g-3 align-items-center">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="امسح الباركود أو أدخل الرمز يدوياً..." id="barcodeInput" autofocus>
                                    <button class="btn btn-primary" type="button" id="scanButton"><i class="fas fa-search"></i></button>
                                    <button class="btn btn-success" type="button" id="testBarcodeButton" title="اختبار الماسح الليزري"><i class="fas fa-barcode"></i></button>
                                </div>
                                <small class="text-muted">اضغط Enter بعد إدخال الباركود أو استخدم ماسح الباركود الليزري</small>
                            </div>
                            <div class="col-md-4 text-end">
                                <button class="btn btn-success" id="quickAddButton">
                                    <i class="fas fa-plus-circle me-2"></i>إضافة سريعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search and Filter -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <input type="text" class="form-control" placeholder="بحث عن منتج..." id="searchInventory">
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="categoryFilter">
                                    <option value="">جميع الفئات</option>
                                    <option value="medicine">أدوية</option>
                                    <option value="supplies">مستلزمات طبية</option>
                                    <option value="cosmetics">مستحضرات تجميل</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="stockFilter">
                                    <option value="">حالة المخزون</option>
                                    <option value="low">منخفض</option>
                                    <option value="out">نفذ</option>
                                    <option value="available">متوفر</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Inventory Table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الكود</th>
                                        <th>اسم المنتج</th>
                                        <th>الفئة</th>
                                        <th>السعر</th>
                                        <th>الكمية</th>
                                        <th>تاريخ الانتهاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="inventoryItems">
                                    <!-- Items will be loaded dynamically -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Product Modal -->
    <div class="modal fade" id="addProductModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة منتج جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addProductForm">
                        <div class="mb-3">
                            <label class="form-label">الكود</label>
                            <div class="input-group">
                                <input type="text" class="form-control" name="code" id="productCode" required>
                                <button class="btn btn-outline-secondary" type="button" id="modalScanButton">
                                    <i class="fas fa-barcode"></i>
                                </button>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">اسم المنتج</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الفئة</label>
                            <select class="form-select" name="category" required>
                                <option value="medicine">أدوية</option>
                                <option value="supplies">مستلزمات طبية</option>
                                <option value="cosmetics">مستحضرات تجميل</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">السعر</label>
                            <input type="number" class="form-control" name="price" step="0.01" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الكمية</label>
                            <input type="number" class="form-control" name="quantity" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">تاريخ الانتهاء</label>
                            <input type="date" class="form-control" name="expiryDate" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="addProductForm" class="btn btn-primary">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Add Modal -->
    <div class="modal fade" id="quickAddModal" tabindex="-1">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة سريعة للمخزون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="quickAddForm">
                        <div class="mb-3">
                            <label class="form-label">الباركود</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="quickAddBarcode" required>
                                <button class="btn btn-outline-secondary" type="button" id="quickScanButton">
                                    <i class="fas fa-barcode"></i>
                                </button>
                            </div>
                            <small class="text-muted">امسح الباركود أو أدخله يدوياً</small>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الكمية المضافة</label>
                            <input type="number" class="form-control" id="quickAddQuantity" value="1" min="1" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" id="confirmQuickAdd" class="btn btn-success">إضافة</button>
                </div>
            </div>
        </div>
    </div>

    <script src="./node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Use CDN for SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="inventory.js"></script>
</body>
</html>