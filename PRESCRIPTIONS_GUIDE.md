# دليل نظام إدارة الوصفات الطبية

## نظرة عامة

تم تطوير نظام شامل لإدارة الوصفات الطبية في تطبيق Automata Pharmacy مع جميع الوظائف المطلوبة لصيدلية احترافية.

## الميزات الرئيسية

### ✅ **إدارة الوصفات**
- إضافة وصفات طبية جديدة
- تعديل الوصفات الموجودة
- حذف الوصفات
- عرض تفاصيل الوصفة كاملة

### ✅ **إدارة الحالات**
- قيد الانتظار (Pending)
- مكتملة (Completed)
- ملغاة (Cancelled)
- تحديث الحالة بنقرة واحدة

### ✅ **البحث والفلترة**
- البحث بالاسم أو رقم الوصفة أو اسم الطبيب
- فلترة حسب الحالة
- فلترة حسب التاريخ
- مسح جميع الفلاتر

### ✅ **إدارة الأدوية**
- إضافة أدوية متعددة لكل وصفة
- تحديد الجرعة والمدة لكل دواء
- إضافة/حذف الأدوية ديناميكياً

## الملفات المضافة/المعدلة

### 1. ملفات جديدة
- `prescriptions.js` - منطق إدارة الوصفات

### 2. ملفات معدلة
- `main.js` - إضافة IPC handlers للوصفات
- `prescriptions.html` - تفعيل جميع الوظائف

## كيفية الاستخدام

### 1. الوصول للنظام
- افتح التطبيق
- اضغط على "الوصفات الطبية" في القائمة الجانبية

### 2. إضافة وصفة جديدة
1. اضغط زر "إضافة وصفة جديدة"
2. املأ البيانات المطلوبة:
   - اسم المريض (مطلوب)
   - رقم الهاتف (اختياري)
   - اسم الطبيب (مطلوب)
   - التاريخ (مطلوب)
3. أضف الأدوية:
   - اسم الدواء (مطلوب)
   - الجرعة (اختياري)
   - المدة (اختياري)
4. أضف ملاحظات إضافية (اختياري)
5. اضغط "حفظ"

### 3. إدارة الأدوية في الوصفة
- **إضافة دواء**: اضغط "إضافة دواء"
- **حذف دواء**: اضغط زر X بجانب الدواء
- **ملاحظة**: يجب أن تحتوي الوصفة على دواء واحد على الأقل

### 4. البحث والفلترة
- **البحث**: اكتب في مربع البحث (اسم المريض، الطبيب، أو رقم الوصفة)
- **فلترة الحالة**: اختر من القائمة المنسدلة
- **فلترة التاريخ**: اختر تاريخ محدد
- **مسح الفلاتر**: اضغط "مسح الفلاتر"

### 5. إدارة الوصفات الموجودة

#### عرض التفاصيل
- اضغط زر العين (👁️) لعرض تفاصيل الوصفة كاملة

#### تعديل الوصفة
- اضغط زر التعديل (✏️)
- عدل البيانات المطلوبة
- اضغط "حفظ"

#### تحديث الحالة
- اضغط زر الصح (✅) لإكمال الوصفة
- الوصفات المكتملة لا يمكن إكمالها مرة أخرى

#### حذف الوصفة
- اضغط زر الحذف (🗑️)
- أكد الحذف في النافذة المنبثقة

## البيانات المحفوظة

### معلومات الوصفة
```javascript
{
  id: 1,                           // معرف فريد
  patientName: "أحمد محمد علي",      // اسم المريض
  phoneNumber: "**********",        // رقم الهاتف
  doctorName: "د. سارة أحمد",        // اسم الطبيب
  date: "2024-06-06",              // تاريخ الوصفة
  medications: [                    // قائمة الأدوية
    {
      name: "باراسيتامول 500مج",
      dosage: "قرص واحد",
      duration: "3 مرات يومياً لمدة 5 أيام"
    }
  ],
  notes: "يُنصح بتناول الدواء بعد الطعام", // ملاحظات
  status: "pending",               // الحالة
  createdAt: "2024-06-06T10:00:00Z", // تاريخ الإنشاء
  updatedAt: "2024-06-06T10:00:00Z", // تاريخ آخر تحديث
  completedAt: "2024-06-06T15:00:00Z" // تاريخ الإكمال (إن وجد)
}
```

### حالات الوصفة
- **pending**: قيد الانتظار (أصفر)
- **completed**: مكتملة (أخضر)
- **cancelled**: ملغاة (أحمر)

## الوظائف التقنية

### في main.js

#### متغيرات البيانات
```javascript
let prescriptions = [];           // قائمة الوصفات
global.lastPrescriptionId = 0;   // آخر معرف وصفة
```

#### IPC Handlers المضافة
- `get-prescriptions` - جلب الوصفات مع الفلاتر
- `add-prescription` - إضافة وصفة جديدة
- `update-prescription` - تحديث وصفة موجودة
- `delete-prescription` - حذف وصفة
- `update-prescription-status` - تحديث حالة الوصفة
- `get-prescription-by-id` - جلب وصفة محددة

### في prescriptions.js

#### الوظائف الرئيسية
- `loadPrescriptions()` - تحميل الوصفات
- `displayPrescriptions()` - عرض الوصفات في الجدول
- `handleAddPrescription()` - معالجة إضافة/تحديث الوصفة
- `filterPrescriptions()` - فلترة الوصفات
- `viewPrescription()` - عرض تفاصيل الوصفة
- `editPrescription()` - تعديل الوصفة
- `deletePrescription()` - حذف الوصفة
- `updateStatus()` - تحديث حالة الوصفة

## البيانات الافتراضية

يتضمن النظام وصفتين تجريبيتين:

### الوصفة الأولى
- **المريض**: أحمد محمد علي
- **الطبيب**: د. سارة أحمد
- **الحالة**: قيد الانتظار
- **الأدوية**: باراسيتامول، أسبرين

### الوصفة الثانية
- **المريض**: فاطمة عبدالله
- **الطبيب**: د. محمد حسن
- **الحالة**: مكتملة
- **الأدوية**: فيتامين د3

## التحسينات المضافة

### واجهة المستخدم
- تصميم متجاوب مع Bootstrap
- أيقونات Font Awesome
- تنبيهات SweetAlert2
- دعم RTL كامل

### تجربة المستخدم
- بحث فوري مع debounce
- تأكيد الحذف
- رسائل نجاح/خطأ واضحة
- نماذج تفاعلية

### الأمان والموثوقية
- التحقق من صحة البيانات
- معالجة الأخطاء
- حفظ تلقائي للبيانات
- نسخ احتياطي

## استكشاف الأخطاء

### إذا لم تظهر الوصفات
1. تحقق من console للأخطاء
2. تأكد من تحميل prescriptions.js
3. تحقق من اتصال IPC

### إذا لم تعمل الفلاتر
1. تحقق من معرفات العناصر
2. تأكد من تحميل event listeners
3. تحقق من دالة debounce

### إذا لم تعمل النماذج
1. تحقق من Bootstrap JS
2. تأكد من SweetAlert2
3. تحقق من معالجات الأحداث

## التطوير المستقبلي

### ميزات مقترحة
- طباعة الوصفات
- ربط مع المخزون
- تذكيرات المتابعة
- إحصائيات الوصفات
- تصدير البيانات

### تحسينات تقنية
- فهرسة البحث
- ضغط البيانات
- نسخ احتياطي تلقائي
- مزامنة السحابة

## الخلاصة

تم تطوير نظام شامل ومتكامل لإدارة الوصفات الطبية يشمل:
- ✅ جميع العمليات الأساسية (CRUD)
- ✅ بحث وفلترة متقدمة
- ✅ واجهة مستخدم احترافية
- ✅ حفظ البيانات التلقائي
- ✅ معالجة الأخطاء
- ✅ تجربة مستخدم ممتازة

النظام جاهز للاستخدام الفوري في البيئة الإنتاجية! 🚀
