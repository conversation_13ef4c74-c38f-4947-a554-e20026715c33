# دليل حفظ البيانات 💾

## ✅ تم إضافة نظام حفظ البيانات الدائم!

### 🎯 **المشكلة التي تم حلها:**
- **قبل**: كانت جميع البيانات تختفي عند إغلاق التطبيق
- **بعد**: يتم حفظ جميع البيانات تلقائياً واستعادتها عند فتح التطبيق

---

## 💾 **البيانات التي يتم حفظها:**

### 1️⃣ **المنتجات (Products)**
- جميع المنتجات المضافة
- الكميات المحدثة
- الأسعار والتفاصيل
- تواريخ الإنشاء والتعديل

### 2️⃣ **المبيعات (Sales)**
- جميع عمليات البيع المكتملة
- تفاصيل كل فاتورة
- المنتجات المباعة والكميات
- طرق الدفع والمبالغ
- التواريخ والأوقات

### 3️⃣ **الإعدادات (Settings)**
- آخر معرف منتج مستخدم
- إعدادات النظام
- تفضيلات المستخدم

---

## 🔄 **كيفية عمل النظام:**

### الحفظ التلقائي:
1. **عند كل عملية**: يتم الحفظ فوراً بعد:
   - إضافة منتج جديد
   - تعديل منتج موجود
   - حذف منتج
   - إتمام عملية بيع
   - إضافة مخزون

2. **حفظ دوري**: كل 30 ثانية تلقائياً

3. **عند الإغلاق**: حفظ شامل قبل إغلاق التطبيق

### الاستعادة التلقائية:
- عند فتح التطبيق، يتم تحميل جميع البيانات المحفوظة
- إذا لم توجد بيانات محفوظة، يتم تحميل البيانات الافتراضية

---

## 📁 **مكان حفظ البيانات:**

### Windows:
```
C:\Users\<USER>\AppData\Roaming\pharmacy-pos\config.json
```

### macOS:
```
~/Library/Preferences/pharmacy-pos/config.json
```

### Linux:
```
~/.config/pharmacy-pos/config.json
```

---

## 🛡️ **الأمان والموثوقية:**

### الحماية من فقدان البيانات:
- **حفظ فوري**: بعد كل عملية مهمة
- **حفظ دوري**: كل 30 ثانية
- **حفظ عند الإغلاق**: ضمان عدم فقدان آخر التغييرات
- **معالجة الأخطاء**: في حالة فشل الحفظ، يتم المحاولة مرة أخرى

### النسخ الاحتياطي:
- يمكن إنشاء نسخة احتياطية من البيانات
- تصدير البيانات بصيغة JSON
- استيراد البيانات من نسخة احتياطية

---

## 📊 **الإحصائيات المتاحة:**

### مبيعات اليوم:
- عدد العمليات
- إجمالي المبيعات
- المنتجات المباعة

### مبيعات الشهر:
- إجمالي العمليات الشهرية
- إجمالي الإيرادات
- متوسط البيع اليومي

### إحصائيات عامة:
- إجمالي المبيعات منذ البداية
- المنتجات الأكثر مبيعاً
- المنتجات منخفضة المخزون

---

## 🔧 **الوظائف الإضافية:**

### إعادة تعيين البيانات:
```javascript
// للمطورين: إعادة تعيين جميع البيانات
await ipcRenderer.invoke('reset-all-data');
```

### النسخ الاحتياطي:
```javascript
// إنشاء نسخة احتياطية
const backup = await ipcRenderer.invoke('backup-data');
```

### الإحصائيات:
```javascript
// الحصول على الإحصائيات
const stats = await ipcRenderer.invoke('get-sales-statistics');
```

---

## 🎮 **للاختبار:**

### اختبار الحفظ:
1. شغل التطبيق
2. أضف منتج جديد
3. أغلق التطبيق
4. أعد فتح التطبيق
5. تحقق من وجود المنتج

### اختبار المبيعات:
1. أضف منتجات للسلة
2. أتمم عملية بيع
3. أغلق التطبيق
4. أعد فتح التطبيق
5. تحقق من تحديث المخزون

### اختبار الاستعادة:
1. احذف ملف البيانات يدوياً
2. أعد فتح التطبيق
3. ستجد البيانات الافتراضية

---

## 🚨 **رسائل النظام:**

### في وحدة التحكم (Console):
- `تم تحميل X منتج من البيانات المحفوظة`
- `تم تحميل X عملية بيع من البيانات المحفوظة`
- `تم حفظ جميع البيانات بنجاح`
- `جاري حفظ البيانات قبل الإغلاق...`
- `تم حفظ عملية البيع رقم X بمبلغ Y ريال`

---

## 📈 **المميزات الجديدة:**

### تتبع التغييرات:
- تاريخ إنشاء كل منتج (`createdAt`)
- تاريخ آخر تعديل (`updatedAt`)
- سجل كامل لجميع المبيعات

### الأداء المحسن:
- حفظ ذكي (فقط عند الحاجة)
- تحميل سريع عند البدء
- ذاكرة محسنة

### سهولة الصيانة:
- ملف واحد لجميع البيانات
- تنسيق JSON قابل للقراءة
- إمكانية التعديل اليدوي (للمطورين)

---

## 🎯 **النتيجة:**

**الآن يمكنك:**
- إغلاق التطبيق بأمان دون فقدان البيانات
- العمل لفترات طويلة مع ضمان الحفظ
- استعادة جميع البيانات عند إعادة التشغيل
- تتبع جميع المبيعات والمخزون
- الحصول على إحصائيات شاملة

**البرنامج الآن جاهز للاستخدام التجاري الكامل مع ضمان حفظ البيانات!** 🎉
