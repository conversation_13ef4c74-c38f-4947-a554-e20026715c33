const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// متغيرات عامة
let prescriptions = [];
let filteredPrescriptions = [];
let currentEditingPrescription = null;

// عناصر DOM
const searchInput = document.getElementById('searchPrescription');
const statusFilter = document.getElementById('statusFilter');
const dateFilter = document.getElementById('dateFilter');
const prescriptionsTable = document.getElementById('prescriptionItems');
const addPrescriptionBtn = document.getElementById('addPrescriptionBtn');
const addPrescriptionForm = document.getElementById('addPrescriptionForm');
const addPrescriptionModal = new bootstrap.Modal(document.getElementById('addPrescriptionModal'));

// تحميل البيانات عند بدء الصفحة
document.addEventListener('DOMContentLoaded', async () => {
    console.log('تحميل صفحة الوصفات الطبية...');
    await loadPrescriptions();
    setupEventListeners();
    
    // تعيين التاريخ الحالي كافتراضي
    const today = new Date().toISOString().split('T')[0];
    document.querySelector('input[name="date"]').value = today;
});

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // البحث والفلترة
    if (searchInput) {
        searchInput.addEventListener('input', debounce(filterPrescriptions, 300));
    }

    if (statusFilter) {
        statusFilter.addEventListener('change', filterPrescriptions);
    }

    if (dateFilter) {
        dateFilter.addEventListener('change', filterPrescriptions);
    }

    // زر إضافة وصفة جديدة
    if (addPrescriptionBtn) {
        addPrescriptionBtn.addEventListener('click', () => {
            currentEditingPrescription = null;
            resetForm();
            addPrescriptionModal.show();
        });
    }

    // نموذج إضافة وصفة
    if (addPrescriptionForm) {
        addPrescriptionForm.addEventListener('submit', handleAddPrescription);
    }

    // منع النقر على العناصر المعطلة في القائمة المنسدلة
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('disabled') || e.target.closest('.disabled')) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
    });

    // إضافة تأثيرات للقائمة المنسدلة
    document.addEventListener('shown.bs.dropdown', function(e) {
        const dropdownMenu = e.target.nextElementSibling;
        if (dropdownMenu && dropdownMenu.classList.contains('dropdown-menu')) {
            dropdownMenu.style.animation = 'dropdownFadeIn 0.2s ease-out';
        }
    });
}

// تحميل الوصفات
async function loadPrescriptions() {
    try {
        const filters = getFilters();
        prescriptions = await ipcRenderer.invoke('get-prescriptions', filters);
        filteredPrescriptions = [...prescriptions];
        displayPrescriptions();
        console.log(`تم تحميل ${prescriptions.length} وصفة طبية`);
    } catch (error) {
        console.error('خطأ في تحميل الوصفات:', error);
        showAlert('خطأ في تحميل الوصفات الطبية', 'error');
    }
}

// عرض الوصفات في الجدول
function displayPrescriptions() {
    if (!prescriptionsTable) return;

    if (filteredPrescriptions.length === 0) {
        prescriptionsTable.innerHTML = `
            <tr>
                <td colspan="6" class="text-center text-muted py-4">
                    <i class="fas fa-file-medical fa-3x mb-3"></i>
                    <br>لا توجد وصفات طبية
                </td>
            </tr>
        `;
        return;
    }

    prescriptionsTable.innerHTML = filteredPrescriptions.map(prescription => `
        <tr>
            <td><strong>#${prescription.id}</strong></td>
            <td>${prescription.patientName}</td>
            <td>${prescription.doctorName}</td>
            <td>${formatDate(prescription.date)}</td>
            <td>
                <span class="badge ${getStatusBadgeClass(prescription.status)}">
                    ${getStatusText(prescription.status)}
                </span>
            </td>
            <td>
                <button class="btn btn-outline-secondary btn-sm" type="button"
                        onclick="showActionsModal(${prescription.id})" title="الإجراءات">
                    <i class="fas fa-cog me-1"></i>الإجراءات
                </button>
            </td>
        </tr>
    `).join('');
}

// الحصول على فلاتر البحث
function getFilters() {
    const filters = {};
    
    if (searchInput && searchInput.value.trim()) {
        filters.searchTerm = searchInput.value.trim();
    }
    
    if (statusFilter && statusFilter.value) {
        filters.status = statusFilter.value;
    }
    
    if (dateFilter && dateFilter.value) {
        filters.dateFrom = dateFilter.value;
        filters.dateTo = dateFilter.value;
    }
    
    return filters;
}

// فلترة الوصفات
async function filterPrescriptions() {
    await loadPrescriptions();
}

// إضافة وصفة جديدة
async function handleAddPrescription(event) {
    event.preventDefault();
    
    try {
        const formData = new FormData(event.target);
        const medications = collectMedications();
        
        const prescriptionData = {
            patientName: formData.get('patientName'),
            phoneNumber: formData.get('phoneNumber'),
            doctorName: formData.get('doctorName'),
            date: formData.get('date'),
            medications: medications,
            notes: formData.get('notes')
        };

        // التحقق من صحة البيانات
        if (!prescriptionData.patientName || !prescriptionData.doctorName || !prescriptionData.date) {
            showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
            return;
        }

        if (medications.length === 0) {
            showAlert('يرجى إضافة دواء واحد على الأقل', 'warning');
            return;
        }

        let result;
        if (currentEditingPrescription) {
            // تحديث وصفة موجودة
            prescriptionData.id = currentEditingPrescription.id;
            result = await ipcRenderer.invoke('update-prescription', prescriptionData);
            showAlert('تم تحديث الوصفة بنجاح', 'success');
        } else {
            // إضافة وصفة جديدة
            result = await ipcRenderer.invoke('add-prescription', prescriptionData);
            showAlert('تم إضافة الوصفة بنجاح', 'success');
        }

        addPrescriptionModal.hide();
        await loadPrescriptions();
        resetForm();
        
    } catch (error) {
        console.error('خطأ في حفظ الوصفة:', error);
        showAlert(error.message || 'فشل في حفظ الوصفة', 'error');
    }
}

// جمع بيانات الأدوية من النموذج
function collectMedications() {
    const medications = [];
    const medicationInputs = document.querySelectorAll('input[name="medications[]"]');
    const dosageInputs = document.querySelectorAll('input[name="dosage[]"]');
    const durationInputs = document.querySelectorAll('input[name="duration[]"]');
    
    for (let i = 0; i < medicationInputs.length; i++) {
        const medication = medicationInputs[i].value.trim();
        if (medication) {
            medications.push({
                name: medication,
                dosage: dosageInputs[i] ? dosageInputs[i].value.trim() : '',
                duration: durationInputs[i] ? durationInputs[i].value.trim() : ''
            });
        }
    }
    
    return medications;
}

// إضافة دواء جديد للقائمة
function addMedication() {
    const medicationsList = document.getElementById('medicationsList');
    const newMedicationRow = document.createElement('div');
    newMedicationRow.className = 'row mb-2';
    newMedicationRow.innerHTML = `
        <div class="col-md-5">
            <input type="text" class="form-control" placeholder="اسم الدواء" name="medications[]" required>
        </div>
        <div class="col-md-3">
            <input type="text" class="form-control" placeholder="الجرعة" name="dosage[]">
        </div>
        <div class="col-md-3">
            <input type="text" class="form-control" placeholder="المدة" name="duration[]">
        </div>
        <div class="col-md-1">
            <button type="button" class="btn btn-danger btn-sm" onclick="removeMedication(this)">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    medicationsList.appendChild(newMedicationRow);
}

// حذف دواء من القائمة
function removeMedication(button) {
    const medicationsList = document.getElementById('medicationsList');
    if (medicationsList.children.length > 1) {
        button.closest('.row').remove();
    } else {
        showAlert('يجب أن تحتوي الوصفة على دواء واحد على الأقل', 'warning');
    }
}

// عرض تفاصيل الوصفة
async function viewPrescription(prescriptionId) {
    try {
        const prescription = await ipcRenderer.invoke('get-prescription-by-id', prescriptionId);
        
        const medicationsHtml = prescription.medications.map(med => `
            <tr>
                <td>${med.name}</td>
                <td>${med.dosage || '-'}</td>
                <td>${med.duration || '-'}</td>
            </tr>
        `).join('');

        const modalHtml = `
            <div class="modal fade" id="viewPrescriptionModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل الوصفة #${prescription.id}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>اسم المريض:</strong> ${prescription.patientName}
                                </div>
                                <div class="col-md-6">
                                    <strong>رقم الهاتف:</strong> ${prescription.phoneNumber || 'غير محدد'}
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>اسم الطبيب:</strong> ${prescription.doctorName}
                                </div>
                                <div class="col-md-6">
                                    <strong>التاريخ:</strong> ${formatDate(prescription.date)}
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>الحالة:</strong> 
                                    <span class="badge ${getStatusBadgeClass(prescription.status)}">
                                        ${getStatusText(prescription.status)}
                                    </span>
                                </div>
                                <div class="col-md-6">
                                    <strong>تاريخ الإنشاء:</strong> ${formatDateTime(prescription.createdAt)}
                                </div>
                            </div>
                            <div class="mb-3">
                                <strong>الأدوية:</strong>
                                <table class="table table-sm mt-2">
                                    <thead>
                                        <tr>
                                            <th>اسم الدواء</th>
                                            <th>الجرعة</th>
                                            <th>المدة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${medicationsHtml}
                                    </tbody>
                                </table>
                            </div>
                            ${prescription.notes ? `
                                <div class="mb-3">
                                    <strong>ملاحظات:</strong>
                                    <p class="mt-2">${prescription.notes}</p>
                                </div>
                            ` : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-info" onclick="addPrescriptionToCart(${prescription.id}); bootstrap.Modal.getInstance(document.getElementById('viewPrescriptionModal')).hide();">
                                <i class="fas fa-shopping-cart me-1"></i>إضافة للسلة
                            </button>
                            <button type="button" class="btn btn-warning" onclick="editPrescription(${prescription.id}); bootstrap.Modal.getInstance(document.getElementById('viewPrescriptionModal')).hide();">تعديل</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إزالة النافذة السابقة إن وجدت
        const existingModal = document.getElementById('viewPrescriptionModal');
        if (existingModal) {
            existingModal.remove();
        }

        // إضافة النافذة الجديدة
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const viewModal = new bootstrap.Modal(document.getElementById('viewPrescriptionModal'));
        viewModal.show();

    } catch (error) {
        console.error('خطأ في عرض الوصفة:', error);
        showAlert('فشل في عرض تفاصيل الوصفة', 'error');
    }
}

// تعديل الوصفة
async function editPrescription(prescriptionId) {
    try {
        const prescription = await ipcRenderer.invoke('get-prescription-by-id', prescriptionId);
        currentEditingPrescription = prescription;

        // ملء النموذج بالبيانات الحالية
        document.querySelector('input[name="patientName"]').value = prescription.patientName;
        document.querySelector('input[name="phoneNumber"]').value = prescription.phoneNumber || '';
        document.querySelector('input[name="doctorName"]').value = prescription.doctorName;
        document.querySelector('input[name="date"]').value = prescription.date;
        document.querySelector('textarea[name="notes"]').value = prescription.notes || '';

        // ملء الأدوية
        const medicationsList = document.getElementById('medicationsList');
        medicationsList.innerHTML = '';

        prescription.medications.forEach((medication, index) => {
            const medicationRow = document.createElement('div');
            medicationRow.className = 'row mb-2';
            medicationRow.innerHTML = `
                <div class="col-md-5">
                    <input type="text" class="form-control" placeholder="اسم الدواء" name="medications[]" value="${medication.name}" required>
                </div>
                <div class="col-md-3">
                    <input type="text" class="form-control" placeholder="الجرعة" name="dosage[]" value="${medication.dosage || ''}">
                </div>
                <div class="col-md-3">
                    <input type="text" class="form-control" placeholder="المدة" name="duration[]" value="${medication.duration || ''}">
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeMedication(this)">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            medicationsList.appendChild(medicationRow);
        });

        // إذا لم تكن هناك أدوية، أضف صف فارغ
        if (prescription.medications.length === 0) {
            addMedication();
        }

        // تغيير عنوان النافذة
        document.querySelector('#addPrescriptionModal .modal-title').textContent = `تعديل الوصفة #${prescription.id}`;

        addPrescriptionModal.show();

    } catch (error) {
        console.error('خطأ في تحميل بيانات الوصفة:', error);
        showAlert('فشل في تحميل بيانات الوصفة للتعديل', 'error');
    }
}

// حذف الوصفة
async function deletePrescription(prescriptionId) {
    const result = await Swal.fire({
        title: 'تأكيد الحذف',
        text: 'هل أنت متأكد من حذف هذه الوصفة؟ لا يمكن التراجع عن هذا الإجراء.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء',
        customClass: {
            popup: 'swal2-rtl'
        }
    });

    if (result.isConfirmed) {
        try {
            await ipcRenderer.invoke('delete-prescription', prescriptionId);
            showAlert('تم حذف الوصفة بنجاح', 'success');
            await loadPrescriptions();
        } catch (error) {
            console.error('خطأ في حذف الوصفة:', error);
            showAlert(error.message || 'فشل في حذف الوصفة', 'error');
        }
    }
}

// تحديث حالة الوصفة
async function updateStatus(prescriptionId, newStatus) {
    try {
        await ipcRenderer.invoke('update-prescription-status', prescriptionId, newStatus);
        showAlert(`تم تحديث حالة الوصفة إلى ${getStatusText(newStatus)}`, 'success');
        await loadPrescriptions();
    } catch (error) {
        console.error('خطأ في تحديث حالة الوصفة:', error);
        showAlert(error.message || 'فشل في تحديث حالة الوصفة', 'error');
    }
}

// عرض نافذة الإجراءات المنبثقة
async function showActionsModal(prescriptionId) {
    try {
        const prescription = await ipcRenderer.invoke('get-prescription-by-id', prescriptionId);

        const actionsHtml = `
            <div class="modal fade" id="actionsModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-file-medical me-2"></i>
                                إدارة الوصفة #${prescription.id}
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body p-0">
                            <div class="list-group list-group-flush">
                                <!-- عرض التفاصيل -->
                                <button type="button" class="list-group-item list-group-item-action d-flex align-items-center p-4"
                                        onclick="viewPrescription(${prescription.id}); closeActionsModal();">
                                    <div class="me-3">
                                        <div class="icon-circle bg-primary">
                                            <i class="fas fa-eye text-white"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">عرض التفاصيل</h6>
                                        <small class="text-muted">عرض جميع معلومات الوصفة والأدوية</small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-light text-dark me-2">1</span>
                                        <i class="fas fa-chevron-left text-muted"></i>
                                    </div>
                                </button>

                                <!-- تعديل الوصفة -->
                                <button type="button" class="list-group-item list-group-item-action d-flex align-items-center p-4"
                                        onclick="editPrescription(${prescription.id}); closeActionsModal();">
                                    <div class="me-3">
                                        <div class="icon-circle bg-warning">
                                            <i class="fas fa-edit text-white"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">تعديل الوصفة</h6>
                                        <small class="text-muted">تعديل بيانات المريض والطبيب والأدوية</small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-light text-dark me-2">2</span>
                                        <i class="fas fa-chevron-left text-muted"></i>
                                    </div>
                                </button>

                                <!-- إضافة للسلة -->
                                <button type="button" class="list-group-item list-group-item-action d-flex align-items-center p-4"
                                        onclick="addPrescriptionToCart(${prescription.id}); closeActionsModal();">
                                    <div class="me-3">
                                        <div class="icon-circle bg-info">
                                            <i class="fas fa-shopping-cart text-white"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">إضافة للسلة</h6>
                                        <small class="text-muted">إضافة جميع الأدوية إلى سلة نقطة البيع</small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-light text-dark me-2">3</span>
                                        <i class="fas fa-chevron-left text-muted"></i>
                                    </div>
                                </button>

                                <!-- فاصل -->
                                <div class="border-top my-2"></div>

                                <!-- إكمال الوصفة -->
                                <button type="button" class="list-group-item list-group-item-action d-flex align-items-center p-4 ${prescription.status === 'completed' ? 'disabled opacity-50' : ''}"
                                        ${prescription.status === 'completed' ? 'disabled' : `onclick="updateStatus(${prescription.id}, 'completed'); closeActionsModal();"`}>
                                    <div class="me-3">
                                        <div class="icon-circle ${prescription.status === 'completed' ? 'bg-secondary' : 'bg-success'}">
                                            <i class="fas fa-check text-white"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">إكمال الوصفة</h6>
                                        <small class="text-muted">${prescription.status === 'completed' ? 'الوصفة مكتملة بالفعل' : 'تحديد الوصفة كمكتملة'}</small>
                                    </div>
                                    ${prescription.status === 'completed' ? '<i class="fas fa-check-circle text-success"></i>' : '<i class="fas fa-chevron-left text-muted"></i>'}
                                </button>

                                <!-- إلغاء الوصفة -->
                                <button type="button" class="list-group-item list-group-item-action d-flex align-items-center p-4 ${prescription.status === 'cancelled' ? 'disabled opacity-50' : ''}"
                                        ${prescription.status === 'cancelled' ? 'disabled' : `onclick="updateStatus(${prescription.id}, 'cancelled'); closeActionsModal();"`}>
                                    <div class="me-3">
                                        <div class="icon-circle ${prescription.status === 'cancelled' ? 'bg-secondary' : 'bg-dark'}">
                                            <i class="fas fa-times text-white"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">إلغاء الوصفة</h6>
                                        <small class="text-muted">${prescription.status === 'cancelled' ? 'الوصفة ملغاة بالفعل' : 'إلغاء الوصفة الطبية'}</small>
                                    </div>
                                    ${prescription.status === 'cancelled' ? '<i class="fas fa-times-circle text-danger"></i>' : '<i class="fas fa-chevron-left text-muted"></i>'}
                                </button>

                                <!-- فاصل -->
                                <div class="border-top my-2"></div>

                                <!-- حذف الوصفة -->
                                <button type="button" class="list-group-item list-group-item-action d-flex align-items-center p-4 text-danger"
                                        onclick="deletePrescription(${prescription.id}); closeActionsModal();">
                                    <div class="me-3">
                                        <div class="icon-circle bg-danger">
                                            <i class="fas fa-trash text-white"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1 text-danger">حذف الوصفة</h6>
                                        <small class="text-muted">حذف نهائي للوصفة من النظام</small>
                                    </div>
                                    <i class="fas fa-chevron-left text-danger"></i>
                                </button>
                            </div>
                        </div>
                        <div class="modal-footer bg-light">
                            <div class="d-flex justify-content-between align-items-center w-100">
                                <div class="d-flex flex-column">
                                    <div class="d-flex align-items-center text-muted mb-1">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <small>المريض: ${prescription.patientName} | الطبيب: ${prescription.doctorName}</small>
                                    </div>
                                    <div class="d-flex align-items-center text-muted">
                                        <i class="fas fa-keyboard me-2"></i>
                                        <small>اختصارات: 1-عرض | 2-تعديل | 3-سلة | ESC-إغلاق</small>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="badge ${getStatusBadgeClass(prescription.status)} me-2">
                                        ${getStatusText(prescription.status)}
                                    </span>
                                    <small class="text-muted">
                                        ${prescription.medications.length} دواء
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إزالة النافذة السابقة إن وجدت
        const existingModal = document.getElementById('actionsModal');
        if (existingModal) {
            existingModal.remove();
        }

        // إضافة النافذة الجديدة
        document.body.insertAdjacentHTML('beforeend', actionsHtml);

        // إظهار النافذة مع تأثيرات
        const actionsModal = new bootstrap.Modal(document.getElementById('actionsModal'), {
            backdrop: 'static',
            keyboard: true
        });

        // إضافة تأثير صوتي (اختياري)
        console.log(`🔧 فتح نافذة إجراءات الوصفة #${prescription.id}`);

        actionsModal.show();

        // إضافة مستمعي الأحداث للنافذة
        const modalElement = document.getElementById('actionsModal');

        // إغلاق النافذة عند الانتهاء
        modalElement.addEventListener('hidden.bs.modal', function () {
            this.remove();
        });

        // اختصارات لوحة المفاتيح
        modalElement.addEventListener('keydown', function(e) {
            switch(e.key) {
                case '1':
                    e.preventDefault();
                    viewPrescription(prescription.id);
                    closeActionsModal();
                    break;
                case '2':
                    e.preventDefault();
                    editPrescription(prescription.id);
                    closeActionsModal();
                    break;
                case '3':
                    e.preventDefault();
                    addPrescriptionToCart(prescription.id);
                    closeActionsModal();
                    break;
                case 'Escape':
                    closeActionsModal();
                    break;
            }
        });

    } catch (error) {
        console.error('خطأ في عرض نافذة الإجراءات:', error);
        showDetailedError(
            'فشل في عرض نافذة الإجراءات',
            'حدث خطأ أثناء محاولة تحميل بيانات الوصفة.',
            error.message
        );
    }
}

// إغلاق نافذة الإجراءات
function closeActionsModal() {
    const actionsModal = document.getElementById('actionsModal');
    if (actionsModal) {
        const modal = bootstrap.Modal.getInstance(actionsModal);
        if (modal) {
            modal.hide();
        }
    }
}

// إضافة الوصفة إلى سلة نقطة البيع
async function addPrescriptionToCart(prescriptionId) {
    try {
        const result = await ipcRenderer.invoke('add-prescription-to-cart', prescriptionId);

        if (result.success) {
            let message = result.message;

            if (result.unavailableItems.length > 0) {
                message += '\n\nالعناصر غير المتوفرة:\n' + result.unavailableItems.join('\n');

                await Swal.fire({
                    title: 'تم إضافة الوصفة جزئياً',
                    text: message,
                    icon: 'warning',
                    confirmButtonText: 'موافق',
                    customClass: {
                        popup: 'swal2-rtl'
                    }
                });
            } else {
                showAlert(message, 'success');
            }

            // عرض خيار الانتقال لنقطة البيع
            const goToPOS = await Swal.fire({
                title: 'تم إضافة الأدوية للسلة',
                text: 'هل تريد الانتقال إلى نقطة البيع لإتمام العملية؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، انتقل لنقطة البيع',
                cancelButtonText: 'لا، البقاء هنا',
                customClass: {
                    popup: 'swal2-rtl'
                }
            });

            if (goToPOS.isConfirmed) {
                window.location.href = 'index.html';
            }

        } else {
            showAlert('فشل في إضافة الوصفة للسلة', 'error');
        }

    } catch (error) {
        console.error('خطأ في إضافة الوصفة للسلة:', error);
        showAlert(error.message || 'فشل في إضافة الوصفة للسلة', 'error');
    }
}

// إعادة تعيين النموذج
function resetForm() {
    if (addPrescriptionForm) {
        addPrescriptionForm.reset();
    }

    // إعادة تعيين قائمة الأدوية
    const medicationsList = document.getElementById('medicationsList');
    if (medicationsList) {
        medicationsList.innerHTML = `
            <div class="row mb-2">
                <div class="col-md-5">
                    <input type="text" class="form-control" placeholder="اسم الدواء" name="medications[]" required>
                </div>
                <div class="col-md-3">
                    <input type="text" class="form-control" placeholder="الجرعة" name="dosage[]">
                </div>
                <div class="col-md-3">
                    <input type="text" class="form-control" placeholder="المدة" name="duration[]">
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeMedication(this)">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
    }

    // إعادة تعيين التاريخ للتاريخ الحالي
    const today = new Date().toISOString().split('T')[0];
    const dateInput = document.querySelector('input[name="date"]');
    if (dateInput) {
        dateInput.value = today;
    }

    // إعادة تعيين عنوان النافذة
    const modalTitle = document.querySelector('#addPrescriptionModal .modal-title');
    if (modalTitle) {
        modalTitle.textContent = 'إضافة وصفة طبية جديدة';
    }

    currentEditingPrescription = null;
}

// دوال مساعدة
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('ar-SA');
}

function getStatusText(status) {
    const statusMap = {
        'pending': 'قيد الانتظار',
        'completed': 'مكتملة',
        'cancelled': 'ملغاة'
    };
    return statusMap[status] || status;
}

function getStatusBadgeClass(status) {
    const classMap = {
        'pending': 'bg-warning',
        'completed': 'bg-success',
        'cancelled': 'bg-danger'
    };
    return classMap[status] || 'bg-secondary';
}

function showAlert(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    };

    // استخدام SweetAlert2 إذا كان متاحاً
    if (typeof Swal !== 'undefined') {
        const icon = type === 'error' ? 'error' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'info';
        Swal.fire({
            title: message,
            icon: icon,
            timer: 3000,
            showConfirmButton: false,
            customClass: {
                popup: 'swal2-rtl'
            }
        });
    } else {
        // fallback للتنبيه العادي
        alert(message);
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
